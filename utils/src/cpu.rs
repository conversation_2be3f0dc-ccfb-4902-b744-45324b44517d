use std::{cmp, thread::available_parallelism};

pub fn max_blocking_threads() -> usize {
    cmp::max(concurrency_cores(30), 1)
}

pub fn concurrency_cores(percent: usize) -> usize {
    match available_parallelism() {
        Ok(result) => {
            let total_cores = result.get();
            if total_cores > 1 {
                let f_percent = percent as f32 / 100 as f32;
                cmp::max(1, (total_cores as f32 * f_percent).round() as usize)
            } else {
                1
            }
        }
        Err(error) => {
            eprintln!("Error Determining core {:?}", error);
            1
        }
    }
}
