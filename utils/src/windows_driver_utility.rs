use crate::constants::{DRIVER_FILE_NAME, DRIVER_NAME, DRIVER_SERVICE_NAME, DRIVER_SYS_NAME};
use crate::dir::get_current_dir;
use anyhow::{anyhow, Result};
use encoding_rs::UTF_16LE;
use std::fs;
use std::path::Path;
use std::{os::windows::process::CommandExt, process::Command};
use tracing::{debug, error, info};

pub fn install(install_path: &str) -> Result<()> {
    let inf = Path::new(install_path);
    if !inf.exists() {
        return Err(std::io::Error::new(std::io::ErrorKind::NotFound, "INF file not found").into());
    }
    // rundll32 setupapi,InstallHinfSection DefaultInstall.NTAMD64 132 "C:\Users\<USER>\Code\windows-kernel-driver\ZirozenDrv\x64\Debug\Z<PERSON>zenDrv\ZirozenDrv.inf"
    let command_str = format!("/add-driver .\\{} /install", DRIVER_FILE_NAME);

    debug!("Running command {}", command_str);
    match Command::new("pnputil.exe")
        .raw_arg(command_str)
        .current_dir(&inf)
        .status()
    {
        Ok(status) if status.success() => {
            info!("Driver installed successfully via pnputil.");
        }
        Ok(status) => {
            error!("pnputil exited with code: {}", status.code().unwrap_or(-1));
        }
        Err(error) => {
            error!(?error, "Failed to run pnputil");
            return Err(anyhow!(format!(
                "Driver uninstallation failed with error: {:?}",
                error
            )));
        }
    };

    Ok(())
}

pub fn has_driver() -> bool {
    get_current_dir().join(DRIVER_FILE_NAME).exists()
}

pub fn is_installed() -> bool {
    let path = format!("C:\\Windows\\System32\\drivers\\{}", DRIVER_SYS_NAME);
    Path::new(&path).exists()
}

pub fn uninstall() -> Result<()> {
    let output = Command::new("fltmc")
        .arg("unload")
        .arg(DRIVER_NAME)
        .spawn()?
        .wait_with_output();

    match output {
        Ok(_) => {
            info!("Driver successfully unloaded");
        }
        Err(error) => {
            error!(?error, "failed to unload driver");
        }
    }

    if let Some(oems) = find_oem_inf_by_driver_name("C:\\Windows\\INF", DRIVER_NAME) {
        for oem in oems {
            let command_str = format!("/delete-driver {} /uninstall", oem);

            debug!("Running Command {}", command_str);

            match Command::new("pnputil").raw_arg(command_str).status() {
                Ok(_) => {
                    info!("Driver with oem {} is uninstalled", oem);
                }
                Err(error) => {
                    error!(?error, "Failed to uninstall driver with oem {}", oem);
                }
            };
        }
    } else {
        error!("No oem with {} driver found", DRIVER_NAME);
    }

    match Command::new("sc.exe")
        .arg("delete")
        .arg(DRIVER_SERVICE_NAME)
        .status()
    {
        Ok(status) if status.success() => {
            info!("Driver service uninstalled successfully.");
        }
        Ok(status) => {
            error!(
                "driver service uninstallation code: {}",
                status.code().unwrap_or(-1)
            );
        }
        Err(error) => {
            error!(?error, "Driver service uninstallation error");
            return Err(anyhow!(format!(
                "Driver service deletion failed with error: {:?}",
                error
            )));
        }
    };

    Ok(())
}

fn read_inf_file_text(path: &Path) -> Option<String> {
    let bytes = fs::read(path).ok()?;

    // Check for UTF-16LE BOM (0xFFFE or 0xFEFF)
    let is_utf16le = bytes.starts_with(&[0xFF, 0xFE]);
    if is_utf16le {
        let (text, _, _) = UTF_16LE.decode(&bytes[2..]);
        Some(text.into_owned())
    } else {
        // Try UTF-8 fallback
        String::from_utf8(bytes).ok()
    }
}

fn find_oem_inf_by_driver_name(search_dir: &str, driver_name: &str) -> Option<Vec<String>> {
    let mut oems = vec![];
    let entries = fs::read_dir(search_dir).ok()?;

    for entry in entries.flatten() {
        let path = entry.path();
        if path.extension().map(|e| e == "inf").unwrap_or(false)
            && path
                .file_name()
                .map(|f| f.to_string_lossy().starts_with("oem"))
                .unwrap_or(false)
        {
            if let Some(text) = read_inf_file_text(&path) {
                if text.contains(driver_name) {
                    oems.push(
                        path.file_name()
                            .map_or("".to_owned(), |f| f.to_string_lossy().to_string()),
                    );
                }
            }
        }
    }

    if oems.len() > 0 {
        return Some(oems);
    }

    None
}
