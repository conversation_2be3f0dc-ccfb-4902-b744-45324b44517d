[package]
name = "utils"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
serde = "1.0.219"
service-manager = "0.8.0"
anyhow = { version = "1.0.98", features = ["backtrace"] }
tracing = { version = "0.1.41", features = ["log", "std"] }
bincode = { version = "2.0.1", features = ["serde"] }
tokio = { version = "1.46.1", features = ["full"] }
serde_json = "1.0.140"
interprocess = { version = "2.2.3", features = ["tokio"] }
read_until_slice = "0.1.13"

[target.'cfg(windows)'.dependencies]
encoding_rs_io = "0.1.7"
encoding_rs = "0.8.35"
