[package]
name = "fim"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
api = { path = "../api" }
utils = { path = "../utils" }
thiserror = "2.0.12"
tokio = { version = "1.46.1", features = ["full", "tracing"] }
anyhow = { version = "1.0.98", features = ["backtrace"] }
tokio-stream = "0.1.17"
serde = "1.0.219"
chrono = "0.4.41"
serde_json = "1.0.140"
globset = "0.4.16"
async-trait = "0.1.88"
notify-debouncer-full = "0.5.0"

[target.'cfg(windows)'.dependencies]
windows_driver_handler = { path = "../windows_driver_handler" }

[target.'cfg(target_os = "linux")'.dependencies]
linux_ebpf = { path = "../linux_ebpf" }
