use database::{
    models::{FIM<PERSON>onfig, FIMConfigType, FileHash},
    Has<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>K<PERSON>, U<PERSON>,
};
#[cfg(target_os = "linux")]
use linux_ebpf::FileEvent;
use logger::{debug, error};
use notify_debouncer_full::notify::{
    event::{
        AccessKind, AccessMode, CreateKind, DataChange, MetadataKind, ModifyKind, RemoveKind,
        RenameMode,
    },
    Event, EventKind,
};
use serde::{Deserialize, Serialize};
#[cfg(windows)]
use windows_driver_handler::WinDriverEvent;

#[derive(Debug, Default, Serialize, Deserialize, Clone, Hash, PartialEq, Eq)]
pub enum Action {
    Any,
    Create,
    Modify,
    Delete,
    Rename,
    Access,
    Block,
    #[default]
    Other,
}

impl From<String> for Action {
    fn from(value: String) -> Self {
        match value.as_str() {
            "ANY" => Action::Any,
            "CREATE" => Action::Create,
            "MODIFY" => Action::Modify,
            "RENAME" => Action::Rename,
            "REMOVE" => Action::Delete,
            "ACCESS" => Action::Access,
            "BLOCK" => Action::Block,
            _ => Action::Other,
        }
    }
}

#[derive(Debug, Serialize, Default, Deserialize, Clone, Hash, PartialEq, Eq)]
pub enum FileType {
    Folder,
    File,
    Process,
    #[default]
    Any,
    Unknown,
}

impl From<CreateKind> for FileType {
    fn from(value: CreateKind) -> Self {
        match value {
            CreateKind::Any => FileType::Any,
            CreateKind::File => FileType::File,
            CreateKind::Folder => FileType::Folder,
            CreateKind::Other => FileType::Unknown,
        }
    }
}

impl From<RemoveKind> for FileType {
    fn from(value: RemoveKind) -> Self {
        match value {
            RemoveKind::Any => FileType::Any,
            RemoveKind::File => FileType::File,
            RemoveKind::Folder => FileType::Folder,
            RemoveKind::Other => FileType::Unknown,
        }
    }
}

fn get_detailed_operation(event_kind: EventKind) -> String {
    match event_kind {
        EventKind::Any => String::from("ANY"),

        EventKind::Create(CreateKind::Any) => String::from("CREATE_ANY"),
        EventKind::Create(CreateKind::File) => String::from("CREATE_FILE"),
        EventKind::Create(CreateKind::Folder) => String::from("CREATE_FOLDER"),
        EventKind::Create(CreateKind::Other) => String::from("CREATE_OTHER"),

        EventKind::Modify(ModifyKind::Any) => String::from("MODIFY_ANY"),
        EventKind::Modify(ModifyKind::Data(DataChange::Any)) => String::from("MODIFY_DATA_ANY"),
        EventKind::Modify(ModifyKind::Data(DataChange::Size)) => String::from("MODIFY_DATA_SIZE"),
        EventKind::Modify(ModifyKind::Data(DataChange::Content)) => {
            String::from("MODIFY_DATA_CONTENT")
        }
        EventKind::Modify(ModifyKind::Data(DataChange::Other)) => String::from("MODIFY_DATA_OTHER"),
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::Any)) => {
            String::from("MODIFY_METADATA_ANY")
        }
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::AccessTime)) => {
            String::from("MODIFY_METADATA_ACCESSTIME")
        }
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::WriteTime)) => {
            String::from("MODIFY_METADATA_WRITETIME")
        }
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::Permissions)) => {
            String::from("MODIFY_METADATA_PERMISSIONS")
        }
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::Ownership)) => {
            String::from("MODIFY_METADATA_OWNERSHIP")
        }
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::Extended)) => {
            String::from("MODIFY_METADATA_EXTENDED")
        }
        EventKind::Modify(ModifyKind::Metadata(MetadataKind::Other)) => {
            String::from("MODIFY_METADATA_OTHER")
        }
        EventKind::Modify(ModifyKind::Name(RenameMode::Any)) => String::from("MODIFY_RENAME_ANY"),
        EventKind::Modify(ModifyKind::Name(RenameMode::To)) => String::from("MODIFY_RENAME_TO"),
        EventKind::Modify(ModifyKind::Name(RenameMode::From)) => String::from("MODIFY_RENAME_FROM"),
        EventKind::Modify(ModifyKind::Name(RenameMode::Both)) => String::from("MODIFY_RENAME_BOTH"),
        EventKind::Modify(ModifyKind::Name(RenameMode::Other)) => {
            String::from("MODIFY_RENAME_OTHER")
        }
        EventKind::Modify(ModifyKind::Other) => String::from("MODIFY_OTHER"),

        EventKind::Remove(RemoveKind::Any) => String::from("REMOVE_ANY"),
        EventKind::Remove(RemoveKind::File) => String::from("REMOVE_FILE"),
        EventKind::Remove(RemoveKind::Folder) => String::from("REMOVE_FOLDER"),
        EventKind::Remove(RemoveKind::Other) => String::from("REMOVE_OTHER"),

        EventKind::Access(AccessKind::Any) => String::from("ACCESS_ANY"),
        EventKind::Access(AccessKind::Read) => String::from("ACCESS_READ"),
        EventKind::Access(AccessKind::Open(AccessMode::Any)) => String::from("ACCESS_OPEN_ANY"),
        EventKind::Access(AccessKind::Open(AccessMode::Execute)) => {
            String::from("ACCESS_OPEN_EXECUTE")
        }
        EventKind::Access(AccessKind::Open(AccessMode::Read)) => String::from("ACCESS_OPEN_READ"),
        EventKind::Access(AccessKind::Open(AccessMode::Write)) => String::from("ACCESS_OPEN_WRITE"),
        EventKind::Access(AccessKind::Open(AccessMode::Other)) => String::from("ACCESS_OPEN_OTHER"),
        EventKind::Access(AccessKind::Close(AccessMode::Any)) => String::from("ACCESS_CLOSE_ANY"),
        EventKind::Access(AccessKind::Close(AccessMode::Execute)) => {
            String::from("ACCESS_CLOSE_EXECUTE")
        }
        EventKind::Access(AccessKind::Close(AccessMode::Read)) => String::from("ACCESS_CLOSE_READ"),
        EventKind::Access(AccessKind::Close(AccessMode::Write)) => {
            String::from("ACCESS_CLOSE_WRITE")
        }
        EventKind::Access(AccessKind::Close(AccessMode::Other)) => {
            String::from("ACCESS_CLOSE_OTHER")
        }
        EventKind::Access(AccessKind::Other) => String::from("ACCESS_OTHER"),

        EventKind::Other => String::from("OTHER"),
    }
}

fn get_operation(detailed_operation: &str) -> String {
    if detailed_operation == "ANY" {
        String::from("ANY")
    } else if detailed_operation.contains("CREATE") {
        String::from("CREATE")
    } else if detailed_operation.contains("MODIFY_RENAME") {
        String::from("RENAME")
    } else if detailed_operation.contains("MODIFY") {
        String::from("MODIFY")
    } else if detailed_operation.contains("REMOVE") {
        String::from("REMOVE")
    } else if detailed_operation.contains("ACCESS") {
        String::from("ACCESS")
    } else {
        String::from("OTHER")
    }
}

#[derive(Debug, Default, Serialize, Deserialize, Clone, PartialEq, Eq, Hash)]
pub struct FIMConfigFile {
    pub config_id: i64,
    pub file_path: String,
    pub ref_name: String,
    pub file_name: String,
}

#[derive(Debug, Default, Serialize, Deserialize, Clone, PartialEq, Eq, Hash)]
pub struct FIMEvent {
    id: PrimaryKey,
    event_id: String,
    action: Action,
    category: String,
    detailed_operation: String,
    username: String,
    hashed: String,
    r#type: FileType,
    pid: u32,
    process_name: String,
    target_path: String,
    rename_from: String,
    event_time: i64,
    config_id: i64,
    config_type: FIMConfigType,
    md5: String,
    sha1: String,
    sha256: String,
    content_change: String,
    size_change: String,
    metadata_change: String,
    fim_config_file: Option<FIMConfigFile>,
    registry_value: Option<String>,
    registry_renamed_value: Option<String>,
    registry_data_type: Option<u32>,
    registry_data: Option<String>,
    registry_data_size: Option<u32>,
}

impl FIMEvent {
    pub async fn get_pending_events(self, limit: u32) -> Vec<Self> {
        let table_name = self.table_name().to_string();
        match self
            .raw_query(format!(
                "SELECT * from {} order by event_time asc limit {}",
                table_name, limit
            ))
            .await
        {
            Ok(value) => value,
            Err(error) => {
                error!(?error, "Failed to get all file_hahses");
                vec![]
            }
        }
    }

    pub fn should_skip(&self) -> bool {
        self.detailed_operation.is_empty() == false
            && self.detailed_operation.starts_with("MODIFY_METADATA")
    }

    pub fn md5(&self) -> &str {
        &self.md5
    }

    pub fn config_type(&self) -> &FIMConfigType {
        &self.config_type
    }

    pub fn config_id(&self) -> &i64 {
        &self.config_id
    }

    pub fn sha1(&self) -> &str {
        &self.sha1
    }

    pub fn set_fim_config_file(&mut self, fim_config_file: FIMConfigFile) {
        self.fim_config_file = Some(fim_config_file);
    }

    pub fn sha256(&self) -> &str {
        &self.sha256
    }

    pub fn set_event_time(&mut self, time: i64) {
        self.event_time = time;
    }

    pub fn set_target_path(&mut self, path: String) {
        self.target_path = path;
    }

    pub async fn generate_hash(&mut self) {
        if let Some(file_hash) = FileHash::generate_hashes(&self.target_path).await {
            self.md5 = file_hash.md5().to_owned();
            self.sha1 = file_hash.sha1().to_owned();
            self.sha256 = file_hash.sha256().to_owned();
        }
    }

    pub fn target_path(&self) -> &str {
        self.target_path.as_str()
    }

    pub fn set_config(&mut self, config: &FIMConfig) {
        self.category = config.category().to_owned();
        self.config_id = config.id();
        self.config_type = config.config_type.clone();
    }

    pub fn generate_id(&mut self) {
        let uuid = Uuid::new_v4().to_string();
        self.id = PrimaryKey::LocalId(uuid.to_owned());
        self.event_id = uuid;
    }

    #[cfg(target_os = "linux")]
    pub async fn build_file_event_from_ebpf_event(event: FileEvent, config: &FIMConfig) -> Self {
        use std::path::Path;

        use linux_ebpf::Operation;

        let mut fs_event = FIMEvent {
            target_path: event.source_path,
            size_change: "no".to_owned(),
            content_change: "no".to_owned(),
            metadata_change: "no".to_owned(),
            hashed: "no".to_owned(),
            category: config.category().to_owned(),
            config_id: config.id(),
            config_type: config.config_type.clone(),
            event_time: event.timestamp as i64,
            process_name: format!("{} {}", event.process.exec, event.process.argv),
            pid: event
                .process
                .pid
                .to_string()
                .parse::<u32>()
                .unwrap_or_default(),
            username: event.process.username,
            ..Default::default()
        };

        let is_file;

        match event.operation {
            Operation::DirCreated => {
                fs_event.r#type = FileType::Folder;
                fs_event.action = Action::Create;
                is_file = false;
            }
            Operation::DirDeleted => {
                fs_event.r#type = FileType::Folder;
                fs_event.action = Action::Delete;
                is_file = false;
            }
            Operation::FileCreated => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Create;
                is_file = true
            }
            Operation::FileDeleted => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Delete;
                is_file = true
            }
            Operation::FileOpened => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Access;
                is_file = true;
            }
            Operation::FileRename => {
                if Path::new(&fs_event.target_path).is_dir() {
                    fs_event.r#type = FileType::Folder;
                    is_file = false;
                } else {
                    fs_event.r#type = FileType::File;
                    is_file = true;
                }
                fs_event.action = Action::Rename;
                fs_event.rename_from = fs_event.target_path;
                fs_event.target_path = event.target_path.unwrap_or_default();
            }
            _ => is_file = false,
        }

        if is_file && Path::new(&fs_event.target_path).exists() {
            if let Some(file_hash) = FileHash::generate_hashes(&fs_event.target_path).await {
                fs_event.hashed = "yes".to_string();
                fs_event.md5 = file_hash.md5().to_owned();
                fs_event.sha1 = file_hash.sha1().to_owned();
                fs_event.sha256 = file_hash.sha256().to_owned();
            } else {
                error!("Failed to generate hash for file {}", fs_event.target_path);
            }
        }

        fs_event
    }

    #[cfg(windows)]
    pub fn build_registry_event_from_win_driver(event: WinDriverEvent, config: &FIMConfig) -> Self {
        use std::path::PathBuf;
        let registry_data = event.get_registry_data().unwrap();

        let mut fs_event = FIMEvent {
            target_path: registry_data.key_name.to_owned(),
            size_change: "no".to_owned(),
            content_change: "no".to_owned(),
            metadata_change: "no".to_owned(),
            hashed: "no".to_owned(),
            category: config.category().to_owned(),
            config_id: config.id(),
            config_type: config.config_type.clone(),
            event_time: registry_data.created_at,
            process_name: registry_data.proc_info.get_name().to_owned(),
            pid: registry_data.proc_info.get_pid(),
            username: registry_data.proc_info.get_user_name().to_owned(),
            r#type: FileType::Any,
            ..Default::default()
        };

        match event.get_event_type() {
            windows_driver_handler::EventType::RegKeyCreate => {
                fs_event.action = Action::Create;
            }
            windows_driver_handler::EventType::RegKeyDelete => {
                fs_event.action = Action::Delete;
            }
            windows_driver_handler::EventType::RegSaveKey => {
                fs_event.action = Action::Modify;
            }
            windows_driver_handler::EventType::RegKeyRename => {
                fs_event.action = Action::Rename;
                fs_event.rename_from = registry_data.key_name.to_owned();
                let full_path = PathBuf::from(registry_data.key_name.to_owned());
                let parent = full_path
                    .parent()
                    .map_or(PathBuf::from(""), |p| p.to_path_buf());
                fs_event.target_path = parent
                    .join(
                        registry_data
                            .updated_key_name
                            .to_owned()
                            .unwrap_or_default(),
                    )
                    .to_string_lossy()
                    .to_string();
            }
            windows_driver_handler::EventType::RegValueCreate => {
                fs_event.action = Action::Create;
                fs_event.registry_value = registry_data.value_name.clone();
                if registry_data
                    .updated_value_name
                    .as_ref()
                    .is_some_and(|f| f.is_empty() == false)
                {
                    fs_event.registry_renamed_value = registry_data.updated_value_name.clone();
                }
                fs_event.registry_data = registry_data.data.clone();
                // fs_event.registry_data_size = Some(registry_data.data_size);
                // fs_event.registry_data_type = Some(registry_data.data_type);
            }
            windows_driver_handler::EventType::RegSetValue => {
                fs_event.action = Action::Modify;
                fs_event.registry_value = registry_data.value_name.clone();
                if registry_data
                    .updated_value_name
                    .as_ref()
                    .is_some_and(|f| f.is_empty() == false)
                {
                    fs_event.registry_renamed_value = registry_data.updated_value_name.clone();
                }
                fs_event.registry_data = registry_data.data.clone();
                // fs_event.registry_data_size = Some(registry_data.data_size);
                // fs_event.registry_data_type = Some(registry_data.data_type);
            }
            windows_driver_handler::EventType::RegValueDelete => {
                fs_event.action = Action::Delete;
                fs_event.registry_value = registry_data.value_name.clone();
            }
            _ => {}
        }

        fs_event
    }

    #[cfg(windows)]
    pub async fn build_file_event_from_win_driver(
        event: WinDriverEvent,
        config: &FIMConfig,
    ) -> Self {
        use std::path::Path;

        let file_data = event.get_file_data().unwrap();

        let mut fs_event = FIMEvent {
            target_path: file_data.file_name.to_owned(),
            size_change: "no".to_owned(),
            content_change: "no".to_owned(),
            metadata_change: "no".to_owned(),
            hashed: "no".to_owned(),
            category: config.category().to_owned(),
            config_id: config.id(),
            config_type: config.config_type.clone(),
            event_time: file_data.created_at,
            process_name: file_data.proc_info.get_name().to_owned(),
            pid: file_data.proc_info.get_pid(),
            username: file_data.proc_info.get_user_name().to_owned(),
            ..Default::default()
        };

        let is_file;

        match event.get_event_type() {
            windows_driver_handler::EventType::FileCreate => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Create;
                is_file = true;
            }
            windows_driver_handler::EventType::FileBlock => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Block;
                is_file = true;
            }
            windows_driver_handler::EventType::FileDelete => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Delete;
                is_file = true;
            }
            windows_driver_handler::EventType::FileRead => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Access;
                is_file = true;
            }
            windows_driver_handler::EventType::FileWrite => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Modify;
                is_file = true;
            }
            windows_driver_handler::EventType::FileRename => {
                fs_event.r#type = FileType::File;
                fs_event.action = Action::Rename;
                is_file = true;
                fs_event.rename_from = file_data.file_name.to_owned();
                fs_event.target_path = file_data.renamed_file_name.to_owned().unwrap_or_default();
            }
            windows_driver_handler::EventType::DirRename => {
                fs_event.r#type = FileType::Folder;
                fs_event.action = Action::Rename;
                fs_event.rename_from = file_data.file_name.to_owned();
                is_file = false;
                fs_event.target_path = file_data.renamed_file_name.to_owned().unwrap_or_default();
            }
            windows_driver_handler::EventType::DirDelete => {
                fs_event.r#type = FileType::Folder;
                fs_event.action = Action::Delete;
                is_file = false;
            }
            windows_driver_handler::EventType::DirCreate => {
                fs_event.r#type = FileType::Folder;
                fs_event.action = Action::Create;
                is_file = false;
            }
            _ => {
                is_file = false;
            }
        }

        if is_file && Path::new(&fs_event.target_path).exists() {
            if let Some(file_hash) = FileHash::generate_hashes(&fs_event.target_path).await {
                fs_event.hashed = "yes".to_string();
                fs_event.md5 = file_hash.md5().to_owned();
                fs_event.sha1 = file_hash.sha1().to_owned();
                fs_event.sha256 = file_hash.sha256().to_owned();
            } else {
                error!("Failed to generate hash for file {}", fs_event.target_path);
            }
        }

        fs_event
    }

    pub async fn build_from(event: Event, config: &FIMConfig) -> Self {
        debug!("Receieved File Event: {:?}", event);
        let event_path_buf = event.paths.last().unwrap();

        let mut fs_event = FIMEvent {
            target_path: event_path_buf.to_string_lossy().to_string(),
            size_change: "no".to_owned(),
            content_change: "no".to_owned(),
            metadata_change: "no".to_owned(),
            hashed: "no".to_owned(),
            category: config.category().to_owned(),
            config_id: config.id(),
            config_type: config.config_type.clone(),
            ..Default::default()
        };

        if event.paths.last().unwrap().extension().is_some() {
            fs_event.r#type = FileType::File
        } else {
            fs_event.r#type = FileType::Folder
        }

        fs_event.detailed_operation = get_detailed_operation(event.kind);
        fs_event.action = get_operation(&fs_event.detailed_operation).into();

        if fs_event.detailed_operation.starts_with("MODIFY_METADATA") {
            fs_event.metadata_change = "yes".to_string();
        }
        if ["MODIFY_DATA_SIZE"].contains(&fs_event.detailed_operation.as_str()) {
            fs_event.size_change = "yes".to_string();
        }
        if ["MODIFY_DATA_CONTENT"].contains(&fs_event.detailed_operation.as_str()) {
            fs_event.content_change = "yes".to_string();
        }

        if [
            "MODIFY_DATA_ANY",
            "MODIFY_DATA_SIZE",
            "MODIFY_DATA_CONTENT",
            "CREATE_FILE",
            "MODIFY_ANY",
            "CREATE_ANY",
        ]
        .contains(&fs_event.detailed_operation.as_str())
        {
            if event_path_buf.is_file() {
                if let Some(file_hash) = FileHash::generate_hashes(&fs_event.target_path).await {
                    fs_event.hashed = "yes".to_string();
                    fs_event.md5 = file_hash.md5().to_owned();
                    fs_event.sha1 = file_hash.sha1().to_owned();
                    fs_event.sha256 = file_hash.sha256().to_owned();
                } else {
                    error!("Failed to generate hash for file {}", fs_event.target_path);
                }
            }
        }

        if ["MODIFY_RENAME_BOTH"].contains(&fs_event.detailed_operation.as_str()) {
            fs_event.rename_from = event
                .paths
                .first()
                .map_or("".to_owned(), |i| i.to_string_lossy().to_string())
        }

        if let Some(id) = event.attrs.process_id() {
            fs_event.pid = id;
            // @TODO get process name and user id
        }

        fs_event
    }
}

impl HasPrimaryKey for FIMEvent {
    fn table_name(&self) -> &str {
        "file_events"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for FIMEvent {}
