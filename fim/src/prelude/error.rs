use anyhow::Error as AnyhowError;
use notify_debouncer_full::notify::<PERSON>rror as NotifyError;
use thiserror::Error;
use tokio::task::JoinError;
#[cfg(windows)]
use windows_driver_handler::WinDriverHandlerError;

#[derive(Error, Debug)]
pub enum FIMError {
    #[error("FIM Error: Failed to collect path from glob pattern {0:?}")]
    FailedToCollectPath(#[from] JoinError),

    #[cfg(windows)]
    #[error("FIM Driver Error: Failed to perform Driver operation {0:?}")]
    WinDriverError(#[from] WinDriverHandlerError),

    #[error("FIM Error: Failed check config file {0:?}")]
    ConfigFileNotFound(String),

    #[error("FIM Error: Error occured in notify {0:?}")]
    NotifyError(#[from] NotifyError),

    #[error("FIM Error: Unknown error")]
    UnknownError(#[from] AnyhowError),
}
