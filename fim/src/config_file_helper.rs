use crate::{fim_event::<PERSON><PERSON><PERSON><PERSON>, FIMConfigFile};
use api::file::UploadedFile;
use database::{
    models::{FIMConfig, FileHash},
    Model,
};
use logger::{debug, error};
use std::path::Path;
use utils::shutdown::is_system_running;

pub struct ConfigFileHelper;

impl ConfigFileHelper {
    async fn generate_hash(path: String) {
        let i_path = path.clone();
        match FileHash::generate_hashes(&i_path).await {
            Some(fs_hash) => {
                let _ = fs_hash.persist().await;
                debug!("generated new hash for {} file {:?}", path, fs_hash);
            }
            None => {
                error!("Failed to generate hashes for file {}", path);
            }
        };
    }

    pub async fn should_send_file(event: &FIMEvent) -> bool {
        let existing_file_hash = FileHash::for_path(event.target_path().to_owned()).await;
        debug!(
            "received existing hash for {} file {:?}",
            event.target_path(),
            existing_file_hash
        );
        if let Some(file_hash) = existing_file_hash {
            if file_hash.md5() == event.md5()
                && file_hash.sha1() == event.sha1()
                && file_hash.sha256() == event.sha256()
            {
                false
            } else {
                ConfigFileHelper::generate_hash(event.target_path().to_owned()).await;
                true
            }
        } else {
            debug!("no existing hash for {} file", event.target_path());
            ConfigFileHelper::generate_hash(event.target_path().to_owned()).await;
            true
        }
    }

    pub async fn send_file(event: &FIMEvent) -> Option<UploadedFile> {
        debug!("Sending file {:?}", event.target_path());
        match api::file::upload(&Path::new(event.target_path())).await {
            Ok(file) => {
                debug!("File sent to API {:?}", file);
                Some(file)
            }
            Err(error) => {
                error!(?error, "Failed to send file to API");
                None
            }
        }
    }

    pub async fn sync_event(config: &FIMConfig, mut event: FIMEvent) {
        if !is_system_running() {
            return;
        }
        if ConfigFileHelper::should_send_file(&event).await {
            if let Some(file) = ConfigFileHelper::send_file(&event).await {
                event.set_fim_config_file(FIMConfigFile {
                    config_id: config.id(),
                    file_path: config
                        .paths()
                        .first()
                        .unwrap()
                        .to_string_lossy()
                        .to_string(),
                    ref_name: file.file_ref,
                    file_name: file.file_name,
                });

                // this event has been persisted and it will be sent to server later
                let _ = event.persist().await;
            }
        }
    }
}
