use crate::{fim_event::FIM<PERSON>vent, glob_paths::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Confi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Watcher};
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use database::{
    models::{FIMConfig, FIMConfigType},
    Model, Uuid,
};
use logger::{debug, error, info, trace, ModuleLogger, WithSubscriber};
use serde_json::json;
use std::{
    collections::{HashMap, HashSet},
    env::current_exe,
    path::PathBuf,
    sync::Arc,
    time::{SystemTime, UNIX_EPOCH},
};
use tokio::{
    fs::File,
    sync::{
        broadcast::Receiver as BroadcastReceiver,
        mpsc::{channel, Receiver, Sender},
        Mutex,
    },
    task::JoinHandle,
};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use utils::{dir::get_log_dir, shutdown::is_system_running};
use windows_driver_handler::{expand_registry_path, WinDriverEvent, WinDriverHandler};

static DRIVER_NAME: &str = "ZirozenDrv";
static FILE_NAME: &str = "\\\\.\\ZirozenDev";
static PORT_NAME: &str = "\\ZirozenPort";

#[derive(Debug, Clone)]
pub struct FIMDriverEvent {
    event: WinDriverEvent,
    config: FIMConfig,
}

impl FIMDriverEvent {
    pub fn new(event: WinDriverEvent, config: FIMConfig) -> Self {
        Self { event, config }
    }

    pub async fn build_fim_event(&self) -> FIMEvent {
        let mut fs_event = if self.event.is_registry_event() {
            FIMEvent::build_registry_event_from_win_driver(self.event.clone(), &self.config)
        } else {
            FIMEvent::build_file_event_from_win_driver(self.event.clone(), &self.config).await
        };
        fs_event.generate_id();
        fs_event
    }
}

pub struct FIMWatcher {
    logger: Arc<ModuleLogger>,
    config: Arc<Vec<FIMConfig>>,
    stop_signal_receiver: Arc<Mutex<BroadcastReceiver<bool>>>,
    endpoint_id: i64,
}

impl FIMWatcher {
    pub fn new(
        config: Vec<FIMConfig>,
        endpoint_id: i64,
        stop_signal_receiver: BroadcastReceiver<bool>,
    ) -> Self {
        Self {
            logger: ModuleLogger::new(
                "fim",
                Some(get_log_dir().join("fim")),
                Some("windows-driver".to_owned()),
            ),
            config: Arc::new(
                config
                    .into_iter()
                    .map(|mut config| {
                        if config.config_type == FIMConfigType::Registry {
                            config.include_path = config
                                .include_path
                                .into_iter()
                                .map(|path| {
                                    PathBuf::from(expand_registry_path(
                                        path.to_string_lossy().to_string().as_str(),
                                    ))
                                })
                                .collect();
                            config.exclude_path = config
                                .exclude_path
                                .into_iter()
                                .map(|path| {
                                    PathBuf::from(expand_registry_path(
                                        path.to_string_lossy().to_string().as_str(),
                                    ))
                                })
                                .collect();
                            config
                        } else {
                            config
                        }
                    })
                    .collect(),
            ),
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            endpoint_id,
        }
    }

    fn remove_old_entries(map: &mut HashMap<String, i64>) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs() as i64;

        map.retain(|_, &mut timestamp| now.saturating_sub(timestamp) <= 5);
    }

    fn start_event_receiver(
        &self,
        rx: Receiver<WinDriverEvent>,
        event_processor: Sender<FIMDriverEvent>,
        paths_to_watch: HashMap<i64, (GlobPaths, GlobPaths, FIMConfig)>,
    ) -> JoinHandle<()> {
        tokio::task::spawn(
            async move {
                let mut excluded_processes = HashSet::new();

                excluded_processes.insert(
                    current_exe()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .to_string(),
                );

                debug!("Excluded processes: {:?}", excluded_processes);

                let mut stream = ReceiverStream::new(rx);

                let mut debounced_event_map = HashMap::new();

                while let Some(event) = stream.next().await.take_if(|_| is_system_running()) {
                    if event.is_process_event() {
                        continue;
                    }
                    let target_path = if event.is_registry_event() {
                        trace!("Received Registry event {:?}", event);
                        PathBuf::from(&event.get_registry_data().as_ref().unwrap().key_name)
                    } else if event.is_file_event() {
                        trace!("Received file event {:?}", event);
                        PathBuf::from(&event.get_file_data().as_ref().unwrap().file_name)
                    } else {
                        PathBuf::from("")
                    };

                    let should_process =
                        match debounced_event_map.insert(event.unique_key(), event.timestamp()) {
                            Some(timestamp) => {
                                if event.timestamp() - timestamp > 5 {
                                    // 5sec passed let's create new event
                                    true
                                } else {
                                    false
                                }
                            }
                            None => true,
                        };
                    trace!("Received File event {:?}", event);
                    if event.should_skip_by_process(&excluded_processes) == false && should_process
                    {
                        let related_configs: HashMap<&i64, &(GlobPaths, GlobPaths, FIMConfig)> =
                            paths_to_watch
                                .iter()
                                .filter(|(_, item)| {
                                    // should match include path and not match exclude path
                                    item.0.matches(target_path.as_path())
                                        && item.1.matches(target_path.as_path()) == false
                                })
                                .collect();

                        // for now considering only first config
                        if let Some((_, value)) = related_configs.iter().next() {
                            debug!(
                                "Matched Config {:?} for path {}",
                                value.2,
                                target_path.display()
                            );
                            if let Err(error) = event_processor
                                .send(FIMDriverEvent::new(event, value.2.clone()))
                                .await
                            {
                                error!(?error, "Failed to send event to processor task");
                            }
                        } else {
                            error!("Failed to find config for event {event:?}");
                        }
                    }
                    FIMWatcher::remove_old_entries(&mut debounced_event_map);
                }
                debug!("Finished Windows Driver Event Receiver Task");
            }
            .with_subscriber(self.logger.subscriber()),
        )
    }

    fn start_event_processor(&self, mut rx: Receiver<FIMDriverEvent>) -> JoinHandle<()> {
        tokio::task::spawn(
            async move {
                while let Some(event) = rx.recv().await {
                    let fim_event = event.build_fim_event().await;
                    debug!("FIM Event: {:?}", fim_event);
                    if event.config.config_type == FIMConfigType::ConfigFile {
                        ConfigFileHelper::sync_event(&event.config, fim_event).await;
                    } else {
                        match fim_event.persist().await {
                            Err(error) => {
                                error!(?error, "Failed to persist event {:?}", event);
                            }
                            _ => {}
                        };
                    }
                }
                debug!("Finished Windows Driver Event Processor Task");
            }
            .with_subscriber(self.logger.subscriber()),
        )
    }

    async fn config_file_initialization(&self, path: &PathBuf, config: &FIMConfig) {
        let mut event = FIMEvent::default();
        event.set_target_path(path.to_string_lossy().to_string());
        event.generate_hash().await;

        if ConfigFileHelper::should_send_file(&event).await {
            if let Some(file) = ConfigFileHelper::send_file(&event).await {
                let event_time = match File::open(path).await {
                    Ok(f) => match f.metadata().await {
                        Ok(metadata) => {
                            Into::<DateTime<Utc>>::into(metadata.created().unwrap()).timestamp()
                        }
                        Err(error) => {
                            error!(?error, "Failed to open file metadata {}", path.display());
                            chrono::Utc::now().timestamp()
                        }
                    },
                    Err(error) => {
                        error!(?error, "Failed to open file {}", path.display());
                        chrono::Utc::now().timestamp()
                    }
                };
                // here send first time file change api
                match send_fim_data(json!({
                    "asset_id" : self.endpoint_id,
                    "data" : {
                        "fim_config_file" : {
                            "file_name" : file.file_name,
                            "file_path" : path.to_string_lossy().to_string(),
                            "event_id" : Uuid::new_v4().to_string(),
                            "event_time" : event_time,
                            "config_id" : config.id(),
                            "ref_name" : file.file_ref
                    }
                  }
                }))
                .await
                {
                    Ok(_) => debug!("Sent first time file change api"),
                    Err(error) => error!(?error, "Failed to send first time file change api"),
                };
            }
        }
    }
}

#[async_trait]
impl Watcher for FIMWatcher {
    async fn watch(&self) -> anyhow::Result<(), FIMError> {
        async {
            info!("---------------------- Starting FIM Windows Driver ------------------------");

            let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

            let mut paths_to_watch = HashMap::new();
            let mut registry_to_watch = HashMap::new();

            let mut all_root_paths = vec![];

            for config in self.config.iter() {
                if config.config_type == FIMConfigType::ConfigFile {
                    if let Some(path) = config.paths().first() {
                        if !path.exists() {
                            error!(
                                "Config file not found at path {}",
                                path.to_string_lossy().to_string()
                            );
                        } else {
                            paths_to_watch.insert(
                                config.id(),
                                (
                                    GlobPaths::new(config.paths().clone()),
                                    GlobPaths::new(config.excluded_paths().clone()),
                                    config.clone(),
                                ),
                            );
                            all_root_paths.extend(config.paths().clone());
                            self.config_file_initialization(path, config).await;
                        }
                    } else {
                        error!("No Config file path is given {:?}", config);
                    }
                } else if config.config_type == FIMConfigType::Registry {
                    registry_to_watch.insert(
                        config.id(),
                        (
                            GlobPaths::new(config.paths().clone()),
                            GlobPaths::new(config.excluded_paths().clone()),
                            config.clone(),
                        ),
                    );
                } else {
                    let included_glob_path = GlobPaths::new(config.paths().clone());
                    let excluded_glob_path = GlobPaths::new(config.excluded_paths().clone());
                    if included_glob_path.is_empty() {
                        error!("No include path is found for config {:?}", config);
                    } else {
                        all_root_paths.extend(included_glob_path.get_root_paths());
                        paths_to_watch.insert(
                            config.id(),
                            (included_glob_path, excluded_glob_path, config.clone()),
                        );
                    }
                }
            }

            debug!("collected paths {:?}", paths_to_watch);
            debug!("collected registries {:?}", registry_to_watch);

            if paths_to_watch.len() == 0 && registry_to_watch.len() == 0 {
                info!("No config is found to watch");
                shutdown_receiver.recv().await.ok();
                return Ok(());
            }

            // load driver

            if let Err(error) = WinDriverHandler::load(DRIVER_NAME) {
                error!(?error, "Failed to load driver");
                shutdown_receiver.recv().await.ok();
                return Err(error.into());
            }

            let (tx, rx) = channel(10);

            let driver_handler = WinDriverHandler::new(DRIVER_NAME, FILE_NAME, PORT_NAME, tx);

            if let Err(error) = driver_handler.unwatch_folder("") {
                error!(?error, "Failed to clear all folder from driver watch list");
            } else {
                debug!("Cleared all file system paths from driver list");
            }
            if let Err(error) = driver_handler.unwatch_registry("") {
                error!(
                    ?error,
                    "Failed to clear all registry paths from driver watch list"
                );
            } else {
                debug!("Cleared all registry paths from driver list");
            }

            // watch file paths
            for (id, paths) in paths_to_watch.iter() {
                info!("Processing include paths for config {}", id);
                for path in paths.0.get_root_paths() {
                    match driver_handler.watch_folder(&path) {
                        Ok(()) => {
                            debug!("Watching path {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to watch path {}", path.display());
                        }
                    };
                }
            }

            // watch registry
            for (id, paths) in registry_to_watch.iter() {
                info!("Processing include registry for config {}", id);
                for path in paths.0.get_root_paths() {
                    match driver_handler.watch_registry(&path) {
                        Ok(()) => {
                            debug!("Watching registry at {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to watch registry {}", path.display());
                        }
                    };
                }
                for path in paths.1.get_root_paths() {
                    match driver_handler.unwatch_registry(&path) {
                        Ok(()) => {
                            debug!("Unwatching registry at {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to watch registry {}", path.display());
                        }
                    };
                }
            }

            let driver_controller = match driver_handler.start() {
                Ok(controller) => controller,
                Err(error) => {
                    error!(?error, "Failed to start windows driver handler");
                    shutdown_receiver.recv().await.ok();
                    return Ok(());
                }
            };

            // unify configuration
            paths_to_watch.extend(registry_to_watch);

            let (event_processor_tx, event_processor_rx) = channel(10);
            let mut event_receiver_task =
                self.start_event_receiver(rx, event_processor_tx, paths_to_watch);
            let mut event_processor_task = self.start_event_processor(event_processor_rx);

            tokio::select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    debug!("Windows driver watcher received shutdown event");
                },

                result = &mut event_receiver_task => {
                    match result {
                        Ok(()) => {
                            debug!("Windows Driver event receiver has finished execution");
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to join windows driver event receiver thread"
                            );
                            shutdown_receiver.recv().await.ok();
                        }
                    }
                },

                result = &mut event_processor_task => {
                    match result {
                        Ok(()) => {
                            debug!("Windows Driver event processor task has finished execution");
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to join windows driver event processor task"
                            );
                            shutdown_receiver.recv().await.ok();
                        }
                    }
                }
            }

            driver_controller.stop();
            if let Err(error) = WinDriverHandler::unload(DRIVER_NAME) {
                error!(?error, "Failed to unload driver");
            } else {
                debug!("Unloaded driver successfully");
            }
            debug!("Stopped Windows FIM Watcher");
            Ok(())
        }
        .with_subscriber(self.logger.subscriber())
        .await
    }
}
