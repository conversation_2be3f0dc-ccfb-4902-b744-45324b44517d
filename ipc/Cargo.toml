[package]
name = "ipc"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
serde = { version = "1.0.219", features = ["derive"] }
anyhow = { version = "1.0.98", features = ["backtrace"] }
tokio = { version = "1.46.1", features = ["full"] }
futures = "0.3.31"
serde_json = "1.0.140"
interprocess = { version = "2.2.3", features = ["tokio"] }
read_until_slice = "0.1.13"
