use crate::{IpcMessage, EOF_MESSAGE};
use logger::{debug, error};
use std::io;
use tokio::io::AsyncReadExt;

use {
    interprocess::local_socket::{
        tokio::{prelude::*, Stream},
        {GenericFilePath, GenericNamespaced, ToNsName},
    },
    tokio::io::{AsyncWriteExt, BufReader},
};

pub async fn send_ipc_message(
    endpoint: &'static str,
    message: IpcMessage,
) -> io::Result<IpcMessage> {
    // Pick a name.
    let name = match if GenericNamespaced::is_supported() {
        endpoint.to_ns_name::<GenericNamespaced>()
    } else {
        format!("/tmp/{}", endpoint).to_fs_name::<GenericFilePath>()
    } {
        Ok(name) => name,
        Err(error) => {
            error!("Error while creating socket name: {error:?}");
            return Err(error);
        }
    };

    // Await this here since we can't do a whole lot without a connection.
    let connection = match Stream::connect(name).await {
        Ok(conn) => conn,
        Err(error) => {
            error!("Error while connecting to proccess socket: {error:?}");
            return Err(error.into());
        }
    };

    // This consumes our connection and splits it into two halves, so that we can concurrently use
    // both.
    let (recver, mut sender) = connection.split();
    let mut recver = BufReader::new(recver);

    let mut data = match serde_json::to_vec(&message) {
        Ok(data) => data,
        Err(error) => {
            error!("Error while serializing message: {error:?}");
            return Err(error.into());
        }
    };

    data.extend(EOF_MESSAGE.as_bytes());

    // Describe the send operation as writing our whole string.
    match sender.write_all(&data).await {
        Err(error) => {
            error!("Error while sending message: {error:?}");
            return Err(error);
        }
        _ => {
            debug!("Message {:?} sent successfully to server", message);
        }
    };

    drop(sender);

    let mut bytes = vec![];

    match recver.read_to_end(&mut bytes).await {
        Ok(read_bytes) => {
            if read_bytes == 0 {
                error!("Bytes read from client process is 0 so unable to process message");
                return Err(io::Error::new(
                    io::ErrorKind::InvalidInput,
                    "Bytes read from client process is 0 so unable to process message",
                ));
            }
        }
        Err(error) => {
            error!("Error while receiving message: {error:?}");
            return Err(error);
        }
    };

    let message: IpcMessage = match serde_json::from_slice(&bytes) {
        Ok(message) => message,
        Err(error) => {
            error!("Error while deserializing response message: {error:?}");
            return Err(io::Error::new(
                io::ErrorKind::InvalidInput,
                "Error while deserializing response message",
            ));
        }
    };

    debug!("Received response from server {:?}", message);

    Ok(message)
}
