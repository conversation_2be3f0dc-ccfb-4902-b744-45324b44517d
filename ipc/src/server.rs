use crate::{IpcMessage, EOF_MESSAGE};
use anyhow::anyhow;
use interprocess::local_socket::{
    tokio::{prelude::*, Stream},
    GenericNamespaced, ListenerOptions, ToNsName,
};
use logger::{debug, error, info, ModuleLogger, WithSubscriber};
use read_until_slice::AsyncBufReadUntilSliceExt;
use std::{io, path::Path, sync::Arc};
use tokio::{
    io::{AsyncWriteExt, BufReader},
    sync::{mpsc::Sender, oneshot},
    task::Jo<PERSON><PERSON><PERSON><PERSON>,
};

pub struct IpcServer {
    endpoint: String,
    stop_sender: Option<oneshot::Sender<()>>,
    message_channel: Sender<IpcMessage>,
    server_handle: Option<JoinHandle<()>>,
    logger: Option<Arc<ModuleLogger>>,
}

impl Drop for IpcServer {
    fn drop(&mut self) {
        if let Err(error) = futures::executor::block_on(async move { self.stop().await }) {
            error!("Error while stopping server: {error:?}");
        }
    }
}

impl IpcServer {
    pub fn new(endpoint: String, message_channel: Sender<IpcMessage>) -> Self {
        Self {
            endpoint,
            stop_sender: None,
            message_channel,
            server_handle: None,
            logger: None,
        }
    }

    // Describe the things we do when we've got a connection ready.
    async fn handle_client(conn: Stream, channel: &Sender<IpcMessage>) -> io::Result<()> {
        let mut recver = BufReader::new(&conn);
        let mut sender = &conn;

        let mut bytes = vec![];

        match recver
            .read_until_slice(EOF_MESSAGE.as_bytes(), &mut bytes)
            .await
        {
            Ok(read_bytes) => {
                if read_bytes == 0 {
                    error!("Bytes read from client process is 0 so unable to process message");
                    return Err(io::Error::new(
                        io::ErrorKind::InvalidInput,
                        "Bytes read from client process is 0 so unable to process message",
                    ));
                }
            }
            Err(error) => {
                error!("Error while receiving message: {error:?}");
                return Err(error);
            }
        };

        let str_message = String::from_utf8_lossy(&bytes).replace(EOF_MESSAGE, "");

        drop(bytes);

        let mut message: IpcMessage = match serde_json::from_str(&str_message) {
            Ok(message) => message,
            Err(error) => {
                error!("Error while deserializing message: {error:?}");
                return Err(io::Error::new(
                    io::ErrorKind::InvalidInput,
                    "Error while deserializing message",
                ));
            }
        };

        match channel.send(message.clone()).await {
            Ok(()) => {
                debug!("Published message {:?} to channel", message);
            }
            Err(error) => {
                error!("Error while sending message to channel: {error:?}");
            }
        };

        debug!("Received message from client {:?}", message);

        message.ack = true;

        let response = match serde_json::to_vec(&message) {
            Ok(response) => response,
            Err(error) => {
                error!("Error while serializing response message: {error:?}");
                return Err(io::Error::new(
                    io::ErrorKind::InvalidInput,
                    "Error while serializing message",
                ));
            }
        };

        match sender.write_all(&response).await {
            Err(error) => {
                error!("Error while sending response message: {error:?}");
                return Err(error);
            }
            _ => {
                debug!("Message {:?} send successfully to client", message);
            }
        };

        Ok(())
    }

    pub async fn stop(&mut self) -> anyhow::Result<()> {
        if let Some(sender) = self.stop_sender.take() {
            if let Err(error) = sender.send(()) {
                error!("Error while sending stop signal: {error:?}");
                return Err(anyhow!("Error while sending stop signal").into());
            }
        }
        if let Some(handle) = self.server_handle.take() {
            if let Err(error) = handle.await {
                error!("Error while joining server handle: {error:?}");
                return Err(error.into());
            }
        }
        if Path::new(format!("/tmp/{}", self.endpoint).as_str()).exists() {
            if let Err(error) = tokio::fs::remove_file(format!("/tmp/{}", self.endpoint)).await {
                error!("Error while removing socket file: {error:?}");
                return Err(error.into());
            }
        }
        Ok(())
    }

    pub fn start(&mut self) -> io::Result<()> {
        if self.server_handle.is_some() {
            error!("Server is already running at endpoint {}", self.endpoint);
            return Ok(());
        }
        let logger = ModuleLogger::new("ipc", None, Some(format!("ipc-{}", self.endpoint)));

        let subscriber = logger.subscriber();

        let handle = logger.with(|| {
            // Pick a name.
            let name = match self.endpoint.clone().to_ns_name::<GenericNamespaced>() {
                Ok(name) => name,
                Err(error) => {
                    error!("Error while creating socket name: {error:?}");
                    return Err(error);
                }
            };

            // Configure our listener...
            let opts = ListenerOptions::new().name(name);

            // ...and create it.
            let listener = match opts.create_tokio() {
                Err(e) if e.kind() == io::ErrorKind::AddrInUse => {
                    // When a program that uses a file-type socket name terminates its socket server
                    // without deleting the file, a "corpse socket" remains, which can neither be
                    // connected to nor reused by a new listener. Normally, Interprocess takes care of
                    // this on affected platforms by deleting the socket file when the listener is
                    // dropped. (This is vulnerable to all sorts of races and thus can be disabled.)
                    //
                    // There are multiple ways this error can be handled, if it occurs, but when the
                    // listener only comes from Interprocess, it can be assumed that its previous instance
                    // either has crashed or simply hasn't exited yet. In this example, we leave cleanup
                    // up to the user, but in a real application, you usually don't want to do that.
                    error!(
                        "Error: could not start server because the socket file is occupied. Please check
                        if {} is in use by another process and try again.",
                        self.endpoint
                    );
                    return Err(e.into());
                }
                x => x?,
            };

            // The syncronization between the server and client, if any is used, goes here.

            let (tx, mut rx) = oneshot::channel::<()>();

            self.stop_sender = Some(tx);

            info!("Starting Server at {}", self.endpoint);

            let channel = self.message_channel.clone();

            let endpoint = self.endpoint.clone();

            let handle = tokio::spawn(async move {
                info!("Starting running at {}", endpoint);
                // Set up our loop boilerplate that processes our incoming connections.
                loop {
                    tokio::select! {
                        biased;

                        _ = &mut rx => {
                            info!("Shutting down IPC server");
                            drop(listener);
                            break;
                        }

                        result = listener.accept() => {
                            match result {
                                Ok(connection) => {
                                     if let Err(error) = IpcServer::handle_client(connection, &channel).await {
                                        error!("Error while handling connection: {error:?}");
                                    }
                                }
                                Err(error) => {
                                    error!("There was an error with an incoming connection {error:?}");
                                    continue;
                                }
                            }
                        }

                    }
                }
            }.with_subscriber(subscriber));

            Ok(handle)
        });

        if let Err(error) = handle {
            error!("Error while starting server: {error:?}");
            return Err(error);
        }

        self.logger = Some(logger);

        self.server_handle = Some(handle.unwrap());

        Ok(())
    }
}
