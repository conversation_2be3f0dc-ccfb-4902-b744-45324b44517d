use agent_manager::AgentError;
use anyhow::Error as AnyhowError;
use api::ApiError;
use database::models::Task;
use serde_json::Error as SerdeError;
use shell::ShellError;
use thiserror::Error;
use tokio::task::JoinError;

#[derive(Error, Debug)]
pub enum TaskExecutionError {
    #[error("Upgrade Error: Unsupported platform")]
    UnsupportedPlatform,

    #[error("Task is not supported: {0:?}")]
    TaskIsNotSupported(Task),

    #[error("Task Execution Error: Failed to create task directory or task output file {0:?}")]
    FailedToCreateTaskFile(#[from] std::io::Error),

    #[error("Task Execution Error: Failed to create task directory or task output file {0}")]
    FailedToRemoveTaskDir(String),

    #[error("Task Execution Error: Received Error From agent {0:?}")]
    AgentError(#[from] AgentError),

    #[error("Task Execution Error: Failed to download file {0:?}")]
    ApiError(#[from] ApiError),

    #[error("Task Execution Error: Given file is not supported {0}")]
    UnsupportedFileExtension(String),

    #[error("Task Execution Error: Failed to extract archive {0}")]
    ExtractionError(String),

    #[error("Task Execution Error: macOS patch scanning error {0}")]
    MacOSPatchScanError(String),

    #[error("Task Execution Error: Windows patch scanning error {0}")]
    WindowsPatchScanError(String),

    #[error("Task Execution Error: Shell Command execution error {0:?}")]
    ShellExecutionError(#[from] ShellError),

    #[error("Task Execution Error: Task join error {0:?}")]
    TaskJoinError(#[from] JoinError),

    #[error("Task Execution Error: Serialize/Deserialize error {0:?}")]
    SerdeError(#[from] SerdeError),

    #[error("Task Execution Error: Agent {0} no command found to execute")]
    NoCommandToExecute(String),

    #[error("Task Execution Error: Task execution error {0:?}")]
    UnknownError(#[from] AnyhowError),
}
