use database::models::Task;

pub trait HasTask: Send + Sync {
    fn get_task(&self) -> &Task;

    fn set_task(&mut self, task: Task);

    fn get_id(&self) -> i64 {
        let task_id = &self.get_task().id;
        task_id.into()
    }

    fn get_name(&self) -> String {
        self.get_task()
            .name
            .as_ref()
            .unwrap_or(&String::from("Unknown"))
            .to_owned()
    }
}
