use super::command_executor::CommandExecutor;
use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable, TaskExecutionError,
};
use anyhow::Result;
use api::patch::send_patch_system_scan_result;
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{AgentMetadata, PatchSystemScanCommands, Task, TaskStatus},
};
use logger::{debug, error, ModuleLogger};
use serde_json::{json, Value};
use shell::ShellOutput;
use std::{collections::HashMap, sync::Arc};
use tokio::fs;
use utils::dir::get_patch_dir;

#[derive(Debug)]
pub struct PatchScanTask {
    task: Task,
    system_scan_output: HashMap<String, Value>,
    api_server_response: Option<Value>,
    metadata: AgentMetadata,
    logger: Arc<ModuleLogger>,
}

impl PatchScanTask {
    pub fn new(task: Task, metadata: AgentMetadata) -> Self {
        Self {
            task,
            system_scan_output: HashMap::new(),
            api_server_response: None,
            metadata,
            logger: ModuleLogger::new("patch_scan", None, Some("patch_scan".to_owned())),
        }
    }

    async fn execute_command(&self, command: &str, use_powershell: bool) -> ShellOutput {
        let command_executor = if use_powershell {
            CommandExecutor::new_powershell(command, Box::new(self))
        } else {
            CommandExecutor::new_command(command, Box::new(self))
        };

        match command_executor.capture().execute().await {
            Ok(shell_result) => {
                if shell_result.failed() {
                    error!(
                        "Command {} failed with output {} and exit code {}",
                        command, shell_result.output, shell_result.exit_code
                    );
                }
                shell_result
            }
            Err(error) => {
                error!(?error, "Failed to run command {}", command);
                self.write_task_log(
                    format!(
                        "Failed to run command command {} with error {:?}",
                        command, error
                    ),
                    Some("ERROR"),
                )
                .await;
                ShellOutput {
                    exit_code: 99,
                    output: format!(
                        "Failed to run command command {} with error {:?}",
                        command, error
                    ),
                }
            }
        }
    }

    async fn run_system_scan_for_patch(
        &self,
        commands: PatchSystemScanCommands,
    ) -> HashMap<String, Value> {
        let mut output = HashMap::new();

        if let Some(cmd_commands) = commands.reg {
            for (key, value) in cmd_commands.iter() {
                cfg_if! {
                    if #[cfg(windows)] {
                        match windows_registry::WinRegistry::new(&value) {
                            Ok(registry) => {
                                output.insert(key.to_owned(), json!(registry.scan_patches(key != "reg_patches")));
                            },
                            Err(error) => {
                                error!(?error, "Failed to read registry values for {}", value);
                            }
                        };
                    } else {
                        error!("Registry command not supported for current platform for key {} and value {}", key, value);
                    }
                }
            }
        }

        if let Some(cmd_commands) = commands.cmd {
            for (key, value) in cmd_commands.iter() {
                output.insert(
                    key.to_owned(),
                    json!(self.execute_command(value, false).await.output),
                );
            }
        }

        if let Some(cmd_commands) = commands.ps {
            for (key, value) in cmd_commands.iter() {
                output.insert(
                    key.to_owned(),
                    json!(self.execute_command(value, true).await.output),
                );
            }
        }

        if let Some(cmd_commands) = commands.wmi {
            for (key, value) in cmd_commands.iter() {
                let cmd = format!("Get-WmiObject -query \"{}\" | ConvertTo-Json", value);
                output.insert(
                    key.to_owned(),
                    json!(self.execute_command(&cmd, true).await.output),
                );
            }
        }

        debug!("Final patch scan scrapping output: {:?}", output);
        output
    }

    async fn send_system_scan_result_to_sever(&self) -> Result<Value, TaskExecutionError> {
        debug!("Saving patch system scan result to local file");

        let patch_directory = get_patch_dir();

        match fs::create_dir_all(&patch_directory).await {
            Err(error) => {
                self.write_task_log(
                    format!("Failed to create directory {:?}", error),
                    Some("ERROR"),
                )
                .await;
            }
            _ => {}
        };

        let _ = fs::write(
            &patch_directory.join("system_scan_output.json"),
            serde_json::to_string_pretty(&self.system_scan_output)?,
        )
        .await;

        debug!("Sending patch system scan result to server");

        match send_patch_system_scan_result(
            self.metadata.get_endpoint_id(),
            &self.system_scan_output,
        )
        .await
        {
            Ok(value) => Ok(value),
            Err(error) => {
                let _ = self
                    .write_task_log(
                        format!("Failed to send response to server {:?}", error),
                        Some("ERROR"),
                    )
                    .await;
                Err(error.into())
            }
        }
    }
}

impl HasTask for PatchScanTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for PatchScanTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for PatchScanTask {}

#[async_trait]
impl TaskExecutable for PatchScanTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of patch_scan {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        if self.task.commands.is_none() {
            error!("No patch scan scrap commands found for {:?}", self.task);
            self.write_task_log("No patch scan commands found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        self.write_task_log("Running commands to gather inrformation".to_owned(), None)
            .await;
        // gather output of given commands
        self.system_scan_output = self
            .run_system_scan_for_patch(self.task.commands.as_ref().unwrap().into())
            .await;

        self.write_task_log("Gathered all inrformation from system".to_owned(), None)
            .await;

        self.api_server_response = Some(self.send_system_scan_result_to_sever().await?);

        self.write_task_log(
            "Sent all gathered inrformation from system to server".to_owned(),
            None,
        )
        .await;

        #[cfg(target_os = "linux")]
        {
            use crate::tasks::patch_scan::resolve_linux_patch_scanner;
            match resolve_linux_patch_scanner(
                self.metadata.get_endpoint_id(),
                self.api_server_response.as_ref().unwrap(),
                Box::new(self),
            )
            .await
            {
                Ok(mut scanner) => scanner.scan().await?,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to determine which linux patch scanner to use"
                    );
                    self.write_task_log(
                        format!(
                            "Failed to determine which linux patch scanner to use {:?}",
                            error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                    return Err(error.into());
                }
            };
        }
        #[cfg(target_os = "macos")]
        {
            use crate::tasks::patch_scan::MacPatchFinder;
            MacPatchFinder::new(
                self.system_scan_output
                    .get("mac_os_version")
                    .unwrap()
                    .as_str()
                    .unwrap(),
                self.metadata.get_endpoint_id(),
                Box::new(self),
            )
            .scan()
            .await?;
        }
        #[cfg(windows)]
        {
            use crate::tasks::patch_scan::WindowsPatchFinder;
            WindowsPatchFinder::new(
                self.metadata.get_endpoint_id(),
                serde_json::to_string(&self.system_scan_output)?,
                Box::new(self),
            )
            .await
            .scan()
            .await?;
        }

        self.write_task_log(
            "Patch scanning has been completed successfully".to_owned(),
            None,
        )
        .await;

        task_result.exit_code = 0;
        task_result.status = TaskStatus::Success;

        Ok(task_result)
    }
}
