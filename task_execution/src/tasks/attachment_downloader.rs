use api::file;
use database::models::FileAttachment;
use logger::{error, info};
use std::path::Path;

use crate::{TaskExecutable, TaskExecutionError, BANDWIDTH_LIMITER};

pub struct AttachmentDownloader<'a> {
    attachment: FileAttachment,
    local_path: Box<Path>,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> AttachmentDownloader<'a> {
    pub fn new(
        attachment: FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
        local_path: Option<Box<Path>>,
    ) -> Self {
        let default_dir = task.get_task_dir();
        Self {
            attachment,
            task,
            local_path: local_path.unwrap_or(default_dir),
        }
    }

    pub async fn download(&self) -> Result<FileAttachment, TaskExecutionError> {
        let mut attachment = self.attachment.clone();

        attachment.local_path = Some(self.local_path.clone());

        info!("Starting to download file {}", attachment.real_name);

        self.task
            .write_task_log(
                format!(
                    "Starting to download file {}",
                    if attachment.real_name.is_empty() {
                        &attachment.ref_name
                    } else {
                        &attachment.real_name
                    }
                ),
                None,
            )
            .await;

        match file::download(attachment, BANDWIDTH_LIMITER.clone()).await {
            Ok(file) => {
                self.task
                    .write_task_log(
                        format!("File {} has been downloaded successfully", file.real_name),
                        None,
                    )
                    .await;
                info!("File {} has been downloaded successfully", file.real_name);
                Ok(file)
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to download attachment {:?}", self.attachment
                );
                self.task
                    .write_task_log(
                        format!("Failed to download file {}", self.attachment.real_name),
                        Some("ERROR"),
                    )
                    .await;
                Err(TaskExecutionError::ApiError(error))
            }
        }
    }
}
