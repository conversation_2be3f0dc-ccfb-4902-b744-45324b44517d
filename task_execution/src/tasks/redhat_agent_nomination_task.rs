use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask,
    tasks::command_executor::CommandExecutor, TaskExecutable,
};
use anyhow::{anyhow, Result};
use api::patch::{upload_redhat_patch_cache_dir, upload_redhat_ssl_certificates};
use async_trait::async_trait;
use async_zip::{tokio::write::ZipFileWriter, Compression, ZipEntryBuilder};
use configparser::ini::WriteOptions;
use database::{
    data_types::TaskResult,
    models::{
        AgentMetadata, FileHash, RedhatNominationHistory, RedhatSyncBlocks, Schedule, Task,
        TaskStatus,
    },
    Model,
};
use futures_util::{future::BoxFuture, FutureExt};
use logger::{debug, error, info, ModuleLogger};
use serde_json::json;
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
};
use tokio::{
    fs::{self, File},
    io::AsyncReadExt,
};
use tokio_util::compat::TokioAsyncReadCompatExt;

const READ_PATH: &str = "/etc/yum.repos.d/redhat.repo";
const WRITE_PATH: &str = "/etc/yum.repos.d/ziro-patch-repo.repo";
const COMMAND_TO_CREATE_CACHE: &str = "yum makecache --disablerepo=* --enablerepo=*-ziro-cache";
const CACHE_PATH: &str = "/var/cache/dnf";

/// Recursively collect all files inside the given directory.
/// This uses BoxFuture to handle recursion.
fn collect_files<'a>(dir: &'a Path) -> BoxFuture<'a, tokio::io::Result<Vec<PathBuf>>> {
    async move {
        let mut files = Vec::new();
        let mut dir_entries = fs::read_dir(dir).await?;
        while let Some(entry) = dir_entries.next_entry().await? {
            let path = entry.path();
            if path.is_dir() {
                files.extend(collect_files(&path).await?);
            } else {
                files.push(path);
            }
        }
        Ok(files)
    }
    .boxed()
}

#[derive(Debug)]
pub struct RedhatAgentNominationTask {
    task: Task,
    logger: Arc<ModuleLogger>,
    agent_metadata: AgentMetadata,
}

impl RedhatAgentNominationTask {
    pub fn new(task: Task, agent_metadata: AgentMetadata) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("redhat_agent_nomination", None, None),
            agent_metadata,
        }
    }

    async fn create_zip(source: &Path, destination: &Path, preserve_root_dir: bool) -> Result<()> {
        let parent_path = if preserve_root_dir {
            source
                .parent()
                .map_or(PathBuf::from(""), |p| p.to_path_buf())
        } else {
            source.to_path_buf()
        };
        debug!("Parent path is {}", parent_path.display());
        let files = match collect_files(&source).await {
            Ok(files) => {
                debug!("Collected files for {}", source.display());
                files
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to collect files from path {}",
                    source.display()
                );
                return Err(error.into());
            }
        };
        let zip_file = match File::create(&destination).await {
            Ok(file) => file,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to create zip file for path {}",
                    source.display()
                );
                return Err(error.into());
            }
        };
        let mut writer = ZipFileWriter::new(zip_file.compat());

        for file_path in files {
            let mut file = match fs::File::open(&file_path).await {
                Ok(file) => file,
                Err(error) => {
                    error!(?error, "Failed to open file {}", file_path.display());
                    continue;
                }
            };
            let mut contents = Vec::new();
            file.read_to_end(&mut contents).await?;

            // Create relative path for ZIP entry
            let relative_path = file_path
                .strip_prefix(parent_path.as_path())?
                .to_string_lossy()
                .replace("\\", "/");

            debug!("Got Relative path inside zip is {}", relative_path);

            let entry = ZipEntryBuilder::new(relative_path.into(), Compression::Deflate);
            writer.write_entry_whole(entry, &contents).await?;
        }

        writer.close().await?;
        Ok(())
    }

    fn get_section_keys() -> Vec<String> {
        vec![
            "enabled".to_owned(),
            "gpgcheck".to_owned(),
            "gpgkey".to_owned(),
            "sslverify".to_owned(),
            "sslcacert".to_owned(),
            "sslclientkey".to_owned(),
            "sslclientcert".to_owned(),
            "metadata_expire".to_owned(),
            "enabled_metadata".to_owned(),
        ]
    }

    async fn build_repo_cache(
        &self,
        repo_blocks: &RedhatSyncBlocks,
    ) -> Result<HashMap<String, Option<String>>> {
        let mut config = configparser::ini::Ini::new();
        // You can easily load a file to get a clone of the map:
        let redhat_repos = match config.load_async(READ_PATH).await {
            Ok(map) => map,
            Err(error) => {
                error!(?error, "Failed to load file {}", READ_PATH);
                self.write_task_log(
                    format!("Failed to load file {} with error {:?}", READ_PATH, error),
                    Some("ERROR"),
                )
                .await;
                return Err(
                    anyhow!("Failed to load file {} with error {}", READ_PATH, error).into(),
                );
            }
        };

        self.write_task_log(
            "Parsed redhat.repo file sections successfully".to_owned(),
            None,
        )
        .await;

        debug!("Successfully Parsed redhat repo");

        let default_redhat_value = match redhat_repos.values().into_iter().next() {
            Some(value) => value.clone(),
            None => {
                error!("Failed to find default redhat repo section");
                self.write_task_log(
                    "Failed to find default redhat repo section".to_owned(),
                    Some("ERROR"),
                )
                .await;
                return Err(anyhow!("Failed to find default redhat repo section").into());
            }
        };

        debug!("Got Redhat section {:?}", default_redhat_value);

        drop(redhat_repos);

        let mut zirozen_repos = configparser::ini::Ini::new();

        for block in repo_blocks.blocks.iter() {
            debug!("Processing block {:?}", block);
            let section = block.reponame.trim_matches(&['[', ']']);
            for key in Self::get_section_keys() {
                if let Some(value) = default_redhat_value.get(key.as_str()) {
                    zirozen_repos.set(section, key.as_str(), value.clone());
                }
            }
            zirozen_repos.set(section, "name", Some(block.displayname.to_owned()));
            zirozen_repos.set(section, "baseurl", Some(block.baseurl.to_owned()));
        }

        // write zirozen repo sections
        let mut write_options = WriteOptions::default();
        write_options.blank_lines_between_sections = 1;

        if let Err(error) = zirozen_repos
            .pretty_write_async(WRITE_PATH, &write_options)
            .await
        {
            error!(
                ?error,
                "Failed to write zirozen repo at path {}", WRITE_PATH
            );
            self.write_task_log(
                format!(
                    "Failed to write zirozen repo at path {} with error {:?}",
                    WRITE_PATH, error
                ),
                Some("ERROR"),
            )
            .await;
            return Err(error.into());
        };

        self.write_task_log(
            format!("Wrote zirozen repo file at path {}", WRITE_PATH),
            None,
        )
        .await;
        self.write_task_log(format!("Creating Cache for zirozen repo"), None)
            .await;

        // run command to build cache

        match CommandExecutor::new_command(COMMAND_TO_CREATE_CACHE, Box::new(self))
            .execute()
            .await
        {
            Ok(output) => {
                debug!(
                    "Command to create cache is executed with output {:?}",
                    output
                );
                if output.succeeded() {
                    self.write_task_log(
                        format!(
                            "Command to create cache is executed successfully with output {:?}",
                            output.output
                        ),
                        None,
                    )
                    .await;
                } else {
                    self.write_task_log(
                        format!(
                            "Failed to execute command to create cache with exit code {}",
                            output.exit_code,
                        ),
                        Some("ERROR"),
                    )
                    .await;
                    return Err(anyhow!("Failed to execute command to create cache with exit code {} and output {:?}",
                            output.exit_code,
                            output.output));
                }
            }
            Err(error) => {
                error!(?error, "Failed to execute command to create cache");
                self.write_task_log(
                    format!(
                        "Failed to execute command to create cache with error {:?}",
                        error
                    ),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        Ok(default_redhat_value)
    }

    async fn zip_and_upload_repo_cache(&self, repo_blocks: &RedhatSyncBlocks) -> Result<()> {
        self.write_task_log(format!("syncing repo cache with server"), None)
            .await;

        let cached_folders = match fs::read_dir(CACHE_PATH).await {
            Ok(mut folders) => {
                let mut directories = vec![];
                while let Ok(Some(dir)) = folders.next_entry().await {
                    if dir.path().is_dir() {
                        directories.push(dir.file_name().into_string().unwrap_or_default());
                    }
                }
                directories
            }
            Err(error) => {
                error!(?error, "Failed to read cache path {}", CACHE_PATH);
                self.write_task_log(
                    format!(
                        "Failed to read cache path {} with error {:?}",
                        CACHE_PATH, error
                    ),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        debug!("Got cached folders as {:?}", cached_folders);

        for block in repo_blocks.blocks.iter() {
            debug!("Processing cache for block {:?}", block.reponame);
            let section = block.reponame.trim_matches(&['[', ']']);
            let selected_folder = cached_folders.iter().find(|item| item.starts_with(section));
            if let Some(folder_name) = selected_folder {
                // here create zip of repodata inside folder name and upload using api
                let destination_path = self.get_task_dir().join(format!("{}.zip", folder_name));

                let source_path = Path::new(CACHE_PATH).join(folder_name).join("repodata");
                debug!("Creating zip of folder {}", source_path.display());
                if let Err(error) =
                    Self::create_zip(&source_path, destination_path.as_path(), true).await
                {
                    error!(
                        ?error,
                        "Failed to create zip of folder {}",
                        source_path.display()
                    );
                    self.write_task_log(
                        format!(
                            "Failed to create zip of folder {} with error {:?}",
                            source_path.display(),
                            error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                    continue;
                }
                debug!("Created zip of folder {}", source_path.display());
                self.write_task_log(
                    format!(
                        "Created zip of folder {} successfully.",
                        source_path.display()
                    ),
                    None,
                )
                .await;
                // here upload that zip
                match upload_redhat_patch_cache_dir(destination_path.as_path(), &block.folderpath)
                    .await
                {
                    Ok(_) => {
                        debug!("Uploaded zip of folder {}", destination_path.display());
                        self.write_task_log(
                            format!("Uploaded zip of folder {} successfully.", folder_name),
                            None,
                        )
                        .await;
                    }
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to upload zip of folder {}",
                            destination_path.display()
                        );
                        self.write_task_log(
                            format!(
                                "Failed to upload zip of folder {} with error {:?}",
                                folder_name, error
                            ),
                            Some("ERROR"),
                        )
                        .await;
                        // mark task as failed
                        return Err(error.into());
                    }
                };
            } else {
                error!(
                    "No folder found for section {} in all folders {:?}",
                    section, cached_folders
                );
            }
        }

        Ok(())
    }

    async fn upload_ssl_certificates(
        &self,
        default_repo_block: HashMap<String, Option<String>>,
    ) -> Result<()> {
        let ssl_dir = self.get_task_dir().join("ssl_certificates");
        match fs::create_dir_all(&ssl_dir).await {
            Ok(_) => {
                debug!("Created ssl dir at path {}", ssl_dir.display());
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to create ssl dir at path {}",
                    ssl_dir.display()
                );
                self.write_task_log(
                    format!(
                        "Failed to create ssl directory at path {} with error {:?}",
                        ssl_dir.display(),
                        error
                    ),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        let keys = ["sslcacert", "sslclientkey", "sslclientcert"];

        for key in keys {
            if let Some(item) = default_repo_block.get(key) {
                if let Some(path) = item {
                    let extension = Path::new(path)
                        .extension()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .to_string();

                    let destination = ssl_dir.join(format!("{}.{}", key, extension));
                    match fs::copy(&path, &destination).await {
                        Ok(_) => {
                            debug!(
                                "Copied file {} to ssl dir at path {}",
                                path,
                                destination.display()
                            );
                            self.write_task_log(format!("Copied file {} to ssl dir", path), None)
                                .await;
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to copy file {} to ssl dir at path {}",
                                path,
                                destination.display()
                            );
                            self.write_task_log(
                                format!(
                                    "Failed to copy file {} to ssl dir with error {:?}",
                                    path, error
                                ),
                                Some("ERROR"),
                            )
                            .await;
                            return Err(error.into());
                        }
                    };
                } else {
                    error!("Failed to find path for key {} in default repo block", key);
                    return Err(anyhow!(
                        "Failed to find path for key {} in default repo block",
                        key
                    )
                    .into());
                }
            } else {
                error!("Failed to find key {} in default repo block", key);
                return Err(anyhow!("Failed to find key {} in default repo block", key).into());
            }
        }

        let ssl_zip = self.get_task_dir().join("ssl_certificates.zip");
        match Self::create_zip(&ssl_dir, &ssl_zip, false).await {
            Ok(_) => {
                debug!("Created zip of ssl dir at path {}", ssl_zip.display());
                self.write_task_log(
                    format!("Created zip of ssl dir at path {}", ssl_zip.display()),
                    None,
                )
                .await;
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to create zip of ssl dir at path {}",
                    ssl_zip.display()
                );
                self.write_task_log(
                    format!(
                        "Failed to create zip of ssl dir at path {} with error {:?}",
                        ssl_zip.display(),
                        error
                    ),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        }

        // now upload ssl zip
        match upload_redhat_ssl_certificates(&ssl_zip, self.agent_metadata.get_endpoint_id()).await
        {
            Ok(_) => {
                debug!("Uploaded zip of ssl {}", ssl_zip.display());
                self.write_task_log(
                    format!("Uploaded zip of ssl {} successfully.", ssl_zip.display()),
                    None,
                )
                .await;
            }
            Err(error) => {
                error!(?error, "Failed to upload zip of ssl {}", ssl_zip.display());
                self.write_task_log(
                    format!(
                        "Failed to upload zip of ssl {} with error {:?}",
                        ssl_zip.display(),
                        error
                    ),
                    Some("ERROR"),
                )
                .await;
            }
        };
        Ok(())
    }

    async fn build_certificate_hashes(
        &self,
        default_repo_block: &HashMap<String, Option<String>>,
    ) -> Result<HashMap<String, String>> {
        let keys = ["sslcacert", "sslclientkey", "sslclientcert"];

        let mut sha256_hashes = HashMap::new();

        for key in keys {
            if let Some(item) = default_repo_block.get(key) {
                if let Some(path) = item {
                    let hash = match FileHash::generate_hashes(&path).await {
                        Some(hash) => hash,
                        None => {
                            error!("Failed to generate hash for file {}", path);
                            return Err(anyhow!("Failed to generate hash for file {}", path).into());
                        }
                    };
                    sha256_hashes.insert(key.to_string(), hash.sha256().to_string());
                } else {
                    error!("Failed to find path for key {} in default repo block", key);
                    return Err(anyhow!(
                        "Failed to find path for key {} in default repo block",
                        key
                    )
                    .into());
                }
            } else {
                error!("Failed to find key {} in default repo block", key);
                return Err(anyhow!("Failed to find key {} in default repo block", key).into());
            }
        }

        Ok(sha256_hashes)
    }

    async fn has_ssl_certificate_changed(
        &self,
        default_repo_block: &HashMap<String, Option<String>>,
        latest_certificate_hashses: &HashMap<String, String>,
    ) -> Result<bool> {
        let keys = ["sslcacert", "sslclientkey", "sslclientcert"];

        let existing_record = match RedhatNominationHistory::get_last_performed_record().await {
            Some(record) => record,
            None => return Ok(true),
        };

        for key in keys {
            if default_repo_block.get(key).is_some() {
                let latest_hash = latest_certificate_hashses
                    .get(key)
                    .map_or("".to_owned(), |v| v.to_owned());
                if key == "sslcacert" {
                    if existing_record.sslcacert_hash != latest_hash {
                        debug!(
                            "sslcert hash has been changed old {} and new {}",
                            existing_record.sslcacert_hash, latest_hash
                        );
                        return Ok(true);
                    }
                } else if key == "sslclientkey" {
                    if existing_record.sslclientkey_hash != latest_hash {
                        debug!(
                            "sslclientkey hash has been changed old {} and new {}",
                            existing_record.sslclientkey_hash, latest_hash
                        );
                        return Ok(true);
                    }
                } else if key == "sslclientcert" {
                    if existing_record.sslclientcert_hash != latest_hash {
                        debug!(
                            "sslclientcert hash has been changed old {} and new {}",
                            existing_record.sslclientcert_hash, latest_hash
                        );
                        return Ok(true);
                    }
                }
            } else {
                error!("Failed to find key {} in default repo block", key);
                return Err(anyhow!("Failed to find key {} in default repo block", key).into());
            }
        }

        Ok(false)
    }
}

impl HasTask for RedhatAgentNominationTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for RedhatAgentNominationTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for RedhatAgentNominationTask {}

#[async_trait]
impl TaskExecutable for RedhatAgentNominationTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!(
                "Initiating execution of Redhat Agent nomination task {}",
                self.get_name()
            ),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        let mut schedule = None;

        if self.get_task().custom_task_details.is_some() {
            if let Some(repo) = self
                .task
                .custom_task_details
                .as_ref()
                .unwrap()
                .get("repoSyncBlocks")
            {
                self.task.redhat_sync_blocks = Some(json!(repo).into());
            }
            if let Some(nomination) = self
                .task
                .custom_task_details
                .as_ref()
                .unwrap()
                .get("nomination")
            {
                match nomination.get("schedule") {
                    Some(schedule_value) => {
                        schedule = Some(Schedule::from(schedule_value.clone()));
                    }
                    None => {
                        error!(
                            "Failed to get schedule from nomination object {:?}",
                            nomination
                        );
                        self.write_task_log(
                            format!(
                                "Failed to get nomination object from task details {:?}",
                                self.task.custom_task_details
                            ),
                            Some("ERROR"),
                        )
                        .await;
                    }
                }
            } else {
                error!(
                    "Failed to get nomination object from task details {:?}",
                    self.task.custom_task_details
                );
                self.write_task_log(
                    format!(
                        "Failed to get nomination object from task details {:?}",
                        self.task.custom_task_details
                    ),
                    Some("ERROR"),
                )
                .await;
            }
        } else {
            // no custom_task_detail found so this agent is no more nominated
            self.write_task_log("No custom task detail found for redhat nomination task so marking agent as not nominated.".to_owned(), None).await;
            match RedhatNominationHistory::default().delete_all().await {
                Ok(_) => {
                    debug!("Redhat Nomination History data deleted successfully");
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to delete existing Redhat Nomination History"
                    );
                }
            }
            task_result.exit_code = 0;
            task_result.status = TaskStatus::Success;
            return Ok(task_result);
        }

        if self.get_task().redhat_sync_blocks.is_none() {
            error!("No redhat sync blocks found for {:?}", self.task);
            self.write_task_log(
                format!("No redhat sync blocks found {:?}", self.get_task()),
                Some("ERROR"),
            )
            .await;
            task_result.exit_code = 99;
            task_result.status = TaskStatus::Failed;
            return Ok(task_result);
        }

        let blocks = self.get_task().redhat_sync_blocks.as_ref().unwrap();

        let redhat_repo_block = match self.build_repo_cache(blocks).await {
            Err(error) => {
                error!(?error, "Failed to build repo cache");
                task_result.exit_code = 99;
                task_result.status = TaskStatus::Failed;
                return Ok(task_result);
            }
            Ok(redhat_first_repo_block) => redhat_first_repo_block,
        };

        let certificate_hashes = self.build_certificate_hashes(&redhat_repo_block).await?;

        debug!("Got hashes as {:?}", certificate_hashes);

        match self.zip_and_upload_repo_cache(blocks).await {
            Err(error) => {
                error!(?error, "Failed to upload repo cache");
                task_result.exit_code = 99;
                task_result.status = TaskStatus::Failed;
                return Ok(task_result);
            }
            Ok(_) => {}
        };

        if self
            .has_ssl_certificate_changed(&redhat_repo_block, &certificate_hashes)
            .await?
        {
            self.write_task_log(
                "SSL Certificate hash has changed so uploading certificate".to_owned(),
                None,
            )
            .await;
            match self.upload_ssl_certificates(redhat_repo_block).await {
                Err(error) => {
                    error!(?error, "Failed to upload ssl certificates");
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                    return Ok(task_result);
                }
                Ok(hashes) => hashes,
            };
        } else {
            self.write_task_log(
                "SSL Certificate hash hasn't changed so skipping certificate upload".to_owned(),
                None,
            )
            .await;
        }

        if let Some(schedule) = schedule {
            let history = RedhatNominationHistory::new(
                TaskStatus::Success,
                certificate_hashes
                    .get("sslcacert")
                    .map_or("".to_owned(), |v| v.to_owned()),
                certificate_hashes
                    .get("sslclientkey")
                    .map_or("".to_owned(), |v| v.to_owned()),
                certificate_hashes
                    .get("sslclientcert")
                    .map_or("".to_owned(), |v| v.to_owned()),
                schedule.get_next_trigger_remaining_secs(),
            )
            .persist()
            .await?;

            info!("History saved to db {:?}", history);
        }

        task_result.exit_code = 0;
        task_result.status = TaskStatus::Success;

        Ok(task_result)
    }
}
