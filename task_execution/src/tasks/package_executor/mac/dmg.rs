use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::{anyhow, Error};
use database::models::FileAttachment;
use logger::{debug, error};
use shell::ShellOutput;
use std::{env::temp_dir, os::unix::fs::PermissionsExt};
use tokio::fs::{self, set_permissions};

pub struct Dmg<'a> {
    commands: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Dmg<'a> {
    pub fn new(
        commands: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            commands,
            attachment,
            task,
        }
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let file_write_result =
            fs::write(temp_dir().join("dmginstaller.sh"), DMG_INSTALL_SCRIPT).await;

        if let Err(error) = file_write_result {
            error!(
                ?error,
                "Failed to write dmg installation script to temp folder"
            );
            Err(anyhow!(
                "Failed to write dmg installation script to temp folder"
            ))
        } else {
            debug!("Wrote dmg installation script");
            set_permissions(
                temp_dir().join("dmginstaller.sh"),
                PermissionsExt::from_mode(0o777),
            )
            .await?;
            debug!("Made dmg installation script as executable");

            let command = self
                .commands
                .get_install_command(self.attachment)
                .map(|i| i.to_owned())
                .unwrap_or(format!(
                    "sh {} '{}'",
                    temp_dir().join("dmginstaller.sh").display(),
                    self.attachment.real_name
                ));

            Ok(CommandExecutor::new_command(&command, self.task)
                .execute()
                .await?)
        }
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let file_write_result =
            fs::write(temp_dir().join("dmguninstaller.sh"), DMG_UNINSTALL_SCRIPT).await;

        if let Err(error) = file_write_result {
            error!(
                ?error,
                "Failed to write dmg uninstall script to temp folder"
            );
            Err(anyhow!(
                "Failed to write dmg uninstall script to temp folder"
            ))
        } else {
            debug!("Wrote dmg uninstallation script");
            set_permissions(
                temp_dir().join("dmguninstaller.sh"),
                PermissionsExt::from_mode(0o777),
            )
            .await?;
            debug!("Made dmg uninstallation script as executable");

            let command = self
                .commands
                .get_uninstall_command(self.attachment)
                .map(|i| i.to_owned())
                .unwrap_or(format!(
                    "sh {} '{}'",
                    temp_dir().join("dmguninstaller.sh").display(),
                    self.attachment.real_name
                ));

            Ok(CommandExecutor::new_command(&command, self.task)
                .execute()
                .await?)
        }
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let file_write_result =
            fs::write(temp_dir().join("dmginstaller.sh"), DMG_INSTALL_SCRIPT).await;

        if let Err(error) = file_write_result {
            error!(
                ?error,
                "Failed to write dmg installation script to temp folder"
            );
            Err(anyhow!(
                "Failed to write dmg installation script to temp folder"
            ))
        } else {
            debug!("Wrote dmg installation script");
            set_permissions(
                temp_dir().join("dmginstaller.sh"),
                PermissionsExt::from_mode(0o777),
            )
            .await?;
            debug!("Made dmg installation script as executable");

            let command = self
                .commands
                .get_upgrade_command(self.attachment)
                .map(|i| i.to_owned())
                .unwrap_or(format!(
                    "sh {} '{}'",
                    temp_dir().join("dmginstaller.sh").display(),
                    self.attachment.real_name
                ));

            Ok(CommandExecutor::new_command(&command, self.task)
                .execute()
                .await?)
        }
    }
}

pub const DMG_INSTALL_SCRIPT: &str = r#"
function installdmg {
    set -x
    listing=$(hdiutil attach "$1" | grep Volumes)
    if [ $? -ne 0 ]; then
        echo "Error: Failed to mount dmg file $1"
        exit 1
    fi
    echo "Mounted volume $listing"
    volume=$(echo "$listing" | cut -f 3)
    if [ -e "$volume"/*.app ]; then
        echo "installing .app file"
        if [ -z "$2" ]; then
            cp -rf "$volume"/*.app /Applications || {
                echo 'Failed to install app' ;
                exit 1;
            }
            for i in "$volume"/*.app; do
                xattr -rc "/Applications/$(basename "$i")"
            done
        else
            cudir=/Users/<USER>/Applications
            [ ! -d $cudir ] && mkdir -p $cudir
            cp -rf "$volume"/*.app $cudir || {
                echo 'Failed to install app' ;
                exit 1;
            }
            for i in "$volume"/*.app; do
                xattr -rc "$cudir/$(basename "$i")"
            done
        fi
        echo "Installation Done."
    elif [ -e "$volume"/*.pkg ]; then
        echo "installing .pkg file"
        package=$(ls -1 "$volume" | grep .pkg | head -1)
        installer -pkg "$volume"/"$package" -target / || {
            echo 'Failed to install app' ;
            exit 1;
        }
        echo "Installation Done."
    fi
    hdiutil detach "$volume"
    echo "Unmounting Device"
    set +x
}
installdmg $1 $2"#;

const DMG_UNINSTALL_SCRIPT: &str = r#"
function uninstalldmg {
    set -x
    listing=$(hdiutil attach $1 | grep Volumes)
    if [ $? -ne 0 ]; then
        echo "Error: Failed to mount dmg file $1"
        exit 1
    fi
    echo "Mounted volume $listing"
    volume=$(echo "$listing" | cut -f 3)
    if [ -e "$volume"/*.app ]; then
        echo "uninstalling .app file"
        if [ -z "$2" ]; then
            for i in "$volume"/*.app; do
                APP_PATH="/Applications/$(basename "$i")"
                echo "$APP_PATH"
                rm -rf "$APP_PATH" || {
                    echo 'Failed to uninstall app' ;
                    exit 1;
                }
            done
        else
            cudir=/Users/<USER>/Applications
            [ ! -d $cudir ] && mkdir -p $cudir
            for i in "$volume"/*.app; do
                APP_PATH="$cudir/$(basename "$i")"
                rm -rf "$APP_PATH" || {
                    echo 'Failed to uninstall app' ;
                    exit 1;
                }
            done
        fi
        echo "Uninstallation Done."
    elif [ -e "$volume"/*.pkg ]; then
        echo "No .app file found in dmg"
    fi
    hdiutil detach "$volume"
    echo "Unmounting Device"
    set +x
}
uninstalldmg $1 $2"#;
