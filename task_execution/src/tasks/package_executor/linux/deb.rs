use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::{anyhow, Error};
use database::models::FileAttachment;
use logger::{debug, error};
use shell::ShellOutput;

pub struct Deb<'a> {
    commands: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Deb<'a> {
    pub fn new(
        commands: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            commands,
            attachment,
            task,
        }
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_install_command(self.attachment)
            .map(|i| i.to_owned())
            .unwrap_or(format!("DEBIAN_FRONTEND=noninteractive dpkg -i *.deb"));

        let command_executor = CommandExecutor::new_command(&command, self.task);

        Ok(command_executor.execute().await?)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let pkg_finder_command =
            format!("dpkg -I {} | grep \"Package:\"", &self.attachment.real_name);
        let pkg_finder_result =
            CommandExecutor::new_command(&pkg_finder_command, self.task.clone())
                .capture()
                .execute()
                .await;

        if let Err(error) = pkg_finder_result {
            error!(?error, "Failed to find package id for deb package");
            Err(anyhow!("Failed to find package id for deb package"))
        } else {
            let package_id = pkg_finder_result.unwrap().output;

            debug!("Got package id output: {}", package_id);

            let command = self
                .commands
                .get_uninstall_command(self.attachment)
                .map(|i| i.to_owned())
                .unwrap_or(format!(
                    "apt-get remove {} --purge -y;apt --fix-broken install -y",
                    package_id.replace("Package:", "").trim()
                ));

            let command_executor = CommandExecutor::new_command(&command, self.task);

            Ok(command_executor.execute().await?)
        }
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_upgrade_command(self.attachment)
            .map(|i| i.to_owned())
            .unwrap_or(format!("DEBIAN_FRONTEND=noninteractive dpkg -i *.deb"));

        let command_executor = CommandExecutor::new_command(&command, self.task);

        Ok(command_executor.execute().await?)
    }
}
