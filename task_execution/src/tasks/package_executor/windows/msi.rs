use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::Error;
use database::models::FileAttachment;
use encoding_rs::UTF_16LE;
use encoding_rs_io::DecodeReaderBytesBuilder;
use logger::{debug, error, info};
use shell::ShellOutput;
use std::{io::Read, path::Path};
use tokio::{fs::OpenOptions, io::AsyncWriteExt};

pub struct Msi<'a> {
    resource: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Msi<'a> {
    pub fn new(
        resource: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            resource,
            attachment,
            task,
        }
    }

    pub fn install_command(&self) -> String {
        let ext = Path::new(&self.attachment.real_name)
            .extension()
            .unwrap()
            .to_str()
            .unwrap()
            .to_lowercase();

        let (command, arguments) = match ext.as_str() {
            "cab" => (
                "DISM.exe",
                format!(
                    "/Online /Add-Package /PackagePath:\"{}\" /LogPath:\"{}\" {}",
                    self.attachment.path_to_file_str(),
                    "msi_log",
                    self.resource
                        .get_install_args(self.attachment)
                        .unwrap_or_default()
                ),
            ),
            "msu" => (
                "wusa.exe",
                format!(
                    "{} {}",
                    self.attachment.real_name,
                    self.resource
                        .get_install_args(self.attachment)
                        .unwrap_or("/quiet /norestart".to_owned())
                ),
            ),
            "mst" => (
                "msiexec.exe",
                format!(
                    "TRANSFORMS={} /L*! \"{}\" {}",
                    self.attachment.real_name,
                    "msi_log",
                    self.resource
                        .get_install_args(self.attachment)
                        .unwrap_or("/qn /norestart".to_owned())
                ),
            ),
            "msi" => (
                "msiexec.exe",
                format!(
                    "/i {} /L*! \"{}\" {}",
                    self.attachment.real_name,
                    "msi_log",
                    self.resource
                        .get_install_args(self.attachment)
                        .unwrap_or("/qn /norestart".to_owned())
                ),
            ),
            "msp" => (
                "msiexec.exe",
                format!(
                    "/update {} /L*! \"{}\" {}",
                    self.attachment.real_name,
                    "msi_log",
                    self.resource
                        .get_install_args(self.attachment)
                        .unwrap_or("/quiet /norestart".to_owned())
                ),
            ),
            "exe" => (
                self.attachment.real_name.as_str(),
                format!(
                    "{}",
                    self.resource
                        .get_install_args(self.attachment)
                        .unwrap_or("/S".to_owned())
                ),
            ),
            _ => {
                error!("Failed to build install command for extension {}", ext);
                ("", "".to_owned())
            }
        };

        format!("{} {}", command, arguments)
    }

    pub fn uninstall_command(&self) -> String {
        let ext = Path::new(&self.attachment.real_name)
            .extension()
            .unwrap()
            .to_str()
            .unwrap()
            .to_lowercase();

        let (command, arguments) = match ext.as_str() {
            "msu" => (
                "wusa.exe",
                format!(
                    "{} {}",
                    self.attachment.real_name,
                    self.resource
                        .get_uninstall_args(self.attachment)
                        .unwrap_or("/quiet /norestart".to_owned())
                ),
            ),
            "mst" => (
                "msiexec.exe",
                format!(
                    "/x TRANSFORMS=\"{}\" /L* \"{}\" {}",
                    self.attachment.real_name,
                    "msi_log",
                    self.resource
                        .get_uninstall_args(self.attachment)
                        .unwrap_or("/qn /norestart".to_owned())
                ),
            ),
            "msi" => (
                "msiexec.exe",
                format!(
                    "/x \"{}\" /L* \"{}\" {}",
                    self.attachment.real_name,
                    "msi_log",
                    self.resource
                        .get_uninstall_args(self.attachment)
                        .unwrap_or("/qn /norestart".to_owned())
                ),
            ),
            "msp" => (
                "msiexec.exe",
                format!(
                    "/x \"{}\" /L* \"{}\" {}",
                    self.attachment.real_name,
                    "msi_log",
                    self.resource
                        .get_uninstall_args(self.attachment)
                        .unwrap_or("/quiet /norestart".to_owned())
                ),
            ),
            _ => {
                error!("Failed to build uninstall command for extension {}", ext);
                ("", "".to_owned())
            }
        };

        format!("{} {}", command, arguments)
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let command = self
            .resource
            .get_install_command(self.attachment)
            .map(|i| i.to_owned())
            .unwrap_or(self.install_command());

        let output = CommandExecutor::new_command(&command, self.task.clone())
            .capture()
            .execute()
            .await?;

        let msi_path = self.task.get_task_dir().join("msi_log");
        if command.contains("msi_log") && msi_path.exists() {
            if let Ok(msi_file) = std::fs::File::open(msi_path) {
                let mut reader = std::io::BufReader::new(
                    DecodeReaderBytesBuilder::new()
                        .encoding(Some(UTF_16LE))
                        .build(msi_file),
                );

                let mut msi_log_content = vec![];

                if let Ok(read_bytes) = reader.read_to_end(&mut msi_log_content) {
                    if read_bytes > 0 {
                        if let Ok(mut log_file) = OpenOptions::new()
                            .read(true)
                            .append(true)
                            .open(self.task.get_log_file())
                            .await
                        {
                            if log_file.write(&msi_log_content).await.is_ok() {
                                debug!("Merged MSI log to main output file");
                            };
                        } else {
                            error!("Failed to write msi log to main output file because of unable to open output log file");
                        }
                    } else {
                        error!("Failed to write msi log to main output file because msi file bytes are 0");
                    }
                } else {
                    error!("Failed to write msi log to main output file because of unable to read msi log file");
                }
            }
        }

        Ok(output)
    }

    async fn uninstall_by_package_names(self) -> Result<ShellOutput, Error> {
        let mut shell_output = ShellOutput::default();

        if let Some(package_names) = self.resource.get_package_names() {
            let mut packages_to_process = vec![];
            // check file exists first
            for package_name in package_names {
                let file_path = Path::new("C:\\Windows\\servicing\\Packages")
                    .join(format!("{}.cat", package_name));
                if !file_path.exists() {
                    info!("Package file {} is not available", file_path.display());
                    self.task
                        .write_task_log(
                            format!(
                                "Package file at path {} does not exist so skipping it",
                                file_path.display()
                            ),
                            None,
                        )
                        .await;
                } else {
                    // we process that package as file is available
                    info!("Package file found at path {}", file_path.display());
                    packages_to_process.push(package_name);
                }
            }

            if packages_to_process.len() == 0 {
                self.task
                    .write_task_log(
                        "Package files found to uninstall is none".to_owned(),
                        Some("ERROR"),
                    )
                    .await;
                shell_output.exit_code = 99;
                return Ok(shell_output);
            }

            for package_name in packages_to_process {
                let command = format!(
                    "dism.exe /Online /Remove-Package /PackageName:{} /norestart",
                    package_name
                );
                info!("Executing command {}", command);
                match CommandExecutor::new_command(&command, self.task.clone())
                    .execute()
                    .await
                {
                    Ok(output) => {
                        shell_output.exit_code = output.exit_code;
                        if output.is_windows_restart_code() {
                            debug!(
                                "Uninstalled package {} successfully with output {}",
                                package_name, output.output
                            );
                            self.task
                                .write_task_log(
                                    format!(
                                        "\nUninstalled package {} successfully with exit code {}",
                                        package_name, output.exit_code
                                    ),
                                    None,
                                )
                                .await;
                        } else {
                            error!(
                                "Failed to uninstall package {} with output {}",
                                package_name, output.output
                            );
                            self.task
                                .write_task_log(
                                    format!(
                                        "\nUninstallation of package {} failed with exit code {}",
                                        package_name, output.exit_code
                                    ),
                                    Some("ERROR"),
                                )
                                .await;
                            // if package name starts with Package_for and fails we mark as failed
                            if package_name.to_lowercase().starts_with("package_for") {
                                return Ok(output);
                            }
                        }
                    }
                    Err(error) => {
                        error!(?error, "Failed to uninstall package {}", package_name);
                        self.task
                            .write_task_log(
                                format!("Failed to uninstall package {}", package_name),
                                Some("ERROR"),
                            )
                            .await;

                        if package_name.to_lowercase().starts_with("package_for") {
                            shell_output.exit_code = 99;
                            return Ok(shell_output);
                        }
                    }
                }
            }
        } else {
            self.task
                .write_task_log(
                    "No package names found to uninstall".to_owned(),
                    Some("ERROR"),
                )
                .await;
            shell_output.exit_code = 99;
        }

        Ok(shell_output)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        // if patch and not third party then use packageNames logic
        if self.resource.is_patch() && !self.resource.is_third_party_path() {
            debug!("Uninstalling patch by package names");
            return self.uninstall_by_package_names().await;
        }
        let command = self
            .resource
            .get_uninstall_command(self.attachment)
            .map(|i| i.to_owned())
            .unwrap_or(self.uninstall_command());

        Ok(CommandExecutor::new_command(&command, self.task)
            .capture()
            .execute()
            .await?)
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self
            .resource
            .get_upgrade_command(self.attachment)
            .map(|i| i.to_owned())
            .unwrap_or(self.install_command());

        Ok(CommandExecutor::new_command(&command, self.task)
            .capture()
            .execute()
            .await?)
    }
}
