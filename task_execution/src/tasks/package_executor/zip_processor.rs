use crate::{
    tasks::{command_executor::CommandExecutor, unzip::Unzip},
    TaskExecutable,
};
use anyhow::Error;
use database::models::Package;
use logger::debug;
use shell::ShellOutput;

pub struct ZipProcessor<'a> {
    package: &'a Package,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> ZipProcessor<'a> {
    pub fn new(package: &'a Package, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self { package, task }
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        Unzip::new(&self.package.pkg_file_path, self.task.clone())
            .extract(None)
            .await?;

        debug!("Archive extracted successfully");

        let package = self.package;

        let command = package
            .install_command
            .as_ref()
            .map(|i| i.to_owned())
            .unwrap_or(
                {
                    cfg_if! {
                        if #[cfg(windows)] {
                            "install.bat"
                        } else {
                            use std::os::unix::fs::PermissionsExt;
                            use tokio::fs::set_permissions;
                            set_permissions(
                                self.package
                                    .pkg_file_path
                                    .disk_local_path()
                                    .join("install.sh"),
                                PermissionsExt::from_mode(0o777),
                            )
                            .await?;
                            "./install.sh"
                        }
                    }
                }
                .to_owned(),
            );

        let command_executor = CommandExecutor::new_command(&command, self.task);

        Ok(command_executor.execute().await?)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        Unzip::new(&self.package.pkg_file_path, self.task.clone())
            .extract(None)
            .await?;

        debug!("Archive extracted successfully");

        let package = self.package;

        let command = package
            .uninstall_command
            .as_ref()
            .map(|i| i.to_owned())
            .unwrap_or(
                {
                    cfg_if! {
                        if #[cfg(windows)] {
                            "uninstall.bat"
                        } else {
                            use std::os::unix::fs::PermissionsExt;
                            use tokio::fs::set_permissions;
                            set_permissions(
                                self.package
                                    .pkg_file_path
                                    .disk_local_path()
                                    .join("uninstall.sh"),
                                PermissionsExt::from_mode(0o777),
                            )
                            .await?;
                            "./uninstall.sh"
                        }
                    }
                }
                .to_owned(),
            );

        let command_executor = CommandExecutor::new_command(&command, self.task);

        Ok(command_executor.execute().await?)
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        Unzip::new(&self.package.pkg_file_path, self.task.clone())
            .extract(None)
            .await?;

        debug!("Archive extracted successfully");

        let package = self.package;

        let command = package
            .upgrade_command
            .as_ref()
            .map(|i| i.to_owned())
            .unwrap_or(
                {
                    cfg_if! {
                        if #[cfg(windows)] {
                            "install.bat"
                        } else {
                            use std::os::unix::fs::PermissionsExt;
                            use tokio::fs::set_permissions;
                            set_permissions(
                                self.package
                                    .pkg_file_path
                                    .disk_local_path()
                                    .join("install.sh"),
                                PermissionsExt::from_mode(0o777),
                            )
                            .await?;
                            "./install.sh"
                        }
                    }
                }
                .to_owned(),
            );

        let command_executor = CommandExecutor::new_command(&command, self.task);

        Ok(command_executor.execute().await?)
    }
}
