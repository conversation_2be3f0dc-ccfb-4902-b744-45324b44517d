use super::command_executor::CommandExecutor;
use crate::{has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable};
use anyhow::{Error, Result};
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{CommandType, LogicalCondition, OutputMatch, Task, TaskStatus, TaskType},
};
use evalexpr::eval_boolean;
use logger::{debug, error, ModuleLogger};
use serde_json::json;
use std::sync::Arc;

#[derive(Debug)]
pub struct ComplianceTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl ComplianceTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("compliance", None, Some("compliance".to_owned())),
        }
    }

    async fn match_output(&self, output: &OutputMatch, result: &str) -> String {
        let match_expression = output.get_expression(result);
        let group_condition = output
            .group_condition
            .as_ref()
            .map_or(LogicalCondition::And, |i| i.to_owned());
        let group_condition = group_condition.expression();
        match output.is_passed(result) {
            Ok(value) => {
                debug!(
                    "Evaluated output match {} with result {}",
                    match_expression, value
                );
                let _ = self
                    .write_task_log(
                        format!(
                            "Evaluated output match {} with result {}",
                            match_expression, value
                        ),
                        None,
                    )
                    .await;
                format!(" {} {}", value, group_condition)
            }
            Err(error) => {
                error!(?error, "Failed to evaluate output match {:?}", output);
                let _ = self
                    .write_task_log(
                        format!(
                            "Failed to evaluate {} with error {:?}",
                            match_expression, error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                format!(" {} {}", "false", group_condition)
            }
        }
    }
}

impl HasTask for ComplianceTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for ComplianceTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for ComplianceTask {}

#[async_trait]
impl TaskExecutable for ComplianceTask {
    async fn execute(&mut self) -> Result<TaskResult, Error> {
        self.write_task_log(
            format!("Initiating execution of compliance {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        if self.get_task().task_type == Some(TaskType::ComplianceTest) {
            if self.get_task().custom_task_details.is_none() {
                error!(
                    "No Compliance detail found for compliance_test {:?}",
                    self.task
                );
                self.write_task_log(
                    "No Compliance detail found for compliance_test".to_owned(),
                    Some("ERROR"),
                )
                .await;
                task_result.status = TaskStatus::Failed;
                task_result.output = "".to_owned();
                task_result.exit_code = 99;
                return Ok(task_result);
            }
            let mut rules = self.task.custom_task_details.clone().unwrap();
            let rules = rules.as_object_mut().unwrap();
            if rules.contains_key("id") == false {
                rules.insert("id".to_owned(), 0.into());
            }
            self.task.compliance = Some(json!(rules).into());
        }

        // if no compliance found return error
        if self.get_task().compliance.is_none() {
            error!("No compliance rules found for {:?}", self.task);
            self.write_task_log("No compliance rules found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let compliance = self.get_task().compliance.as_ref().unwrap();

        let default_string = "".to_owned();
        let mut entire_compliance_expression = "".to_owned();
        // execute each action
        for action in &compliance.rules {
            let command_executor = if compliance.execution_type == Some("script".to_owned()) {
                CommandExecutor::new_script_attachment(
                    action.script_file.as_ref().unwrap(),
                    Box::new(self),
                )
            } else {
                let cmd = action.command.as_ref().unwrap_or_else(|| &default_string);
                if action
                    .command_type
                    .as_ref()
                    .is_some_and(|x| x == &CommandType::Powershell)
                {
                    CommandExecutor::new_powershell(cmd, Box::new(self))
                } else {
                    CommandExecutor::new_command(cmd, Box::new(self))
                }
            };

            let result = command_executor.capture().execute().await?;

            let mut is_succeed = result.succeeded();

            // we match output only if output is given
            if let Some(output_matches) = action.output_matches.as_ref() {
                let mut rule_output_matches_expression = "".to_owned();
                for output_match in output_matches {
                    rule_output_matches_expression.push_str(
                        self.match_output(output_match, &result.output)
                            .await
                            .as_str(),
                    );
                }
                if rule_output_matches_expression.is_empty() == false {
                    let rule_output_matches_expression = rule_output_matches_expression
                        .as_str()
                        .trim()
                        .trim_end_matches("&&")
                        .trim_end_matches("||");

                    match eval_boolean(rule_output_matches_expression) {
                        Ok(result) => {
                            debug!(
                                "Rule output matches expression {} evaluate to {}",
                                rule_output_matches_expression, result
                            );
                            is_succeed = result;
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to evaluate output matches expression {}",
                                rule_output_matches_expression
                            );
                            is_succeed = false;
                        }
                    };
                }
            } else {
                task_result.exit_code = result.exit_code;
            }

            entire_compliance_expression.push_str(
                format!(
                    " {} {}",
                    is_succeed,
                    action
                        .condition
                        .as_ref()
                        .map_or(LogicalCondition::And, |i| i.to_owned())
                        .expression()
                )
                .as_str(),
            );
        }

        let execution_expression = entire_compliance_expression
            .as_str()
            .trim()
            .trim_end_matches("&&")
            .trim_end_matches("||");

        debug!("Expression for All Rules built -> {}", execution_expression);

        match eval_boolean(execution_expression) {
            Ok(result) => {
                if result {
                    task_result.exit_code = 0;
                    task_result.status = TaskStatus::Success;
                } else {
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                }
            }
            Err(e) => {
                error!(
                    "Failed to evaluate expression {}, {:?}",
                    execution_expression, e
                );
                task_result.exit_code = 99;
                task_result.status = TaskStatus::Failed;
                self.write_task_log(
                    format!(
                        "Failed to evaluate expression {}, {:?}",
                        execution_expression, e
                    ),
                    Some("ERROR"),
                )
                .await;
            }
        }

        Ok(task_result)
    }
}
