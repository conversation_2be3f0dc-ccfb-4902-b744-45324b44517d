use crate::{has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable};
use anyhow::Result;
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{CommandType, ConfigurationType, Task, TaskStatus},
};
use logger::{error, ModuleLogger};
use std::sync::Arc;

use super::command_executor::CommandExecutor;

#[derive(Debug)]
pub struct ConfigurationTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl ConfigurationTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("configuration", None, Some("configuration".to_owned())),
        }
    }
}

impl HasTask for ConfigurationTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for ConfigurationTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for ConfigurationTask {}

#[async_trait]
impl TaskExecutable for ConfigurationTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of configuration {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no configuration found return error
        if self.task.configuration.is_none() {
            error!("No configuration found for {:?}", self.task);
            self.write_task_log("No configuration found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let configuration = self.task.configuration.as_ref().unwrap();

        let default_string = "".to_owned();

        // execute each action
        for action in &configuration.configuration_actions {
            let command_executor = if configuration.configuration_type == ConfigurationType::Script
            {
                CommandExecutor::new_script_attachment(
                    action.script_file.as_ref().unwrap(),
                    Box::new(self),
                )
            } else {
                let cmd = action.command.as_ref().unwrap_or_else(|| &default_string);
                if action
                    .command_type
                    .as_ref()
                    .is_some_and(|x| x == &CommandType::Powershell)
                {
                    CommandExecutor::new_powershell(cmd, Box::new(self))
                } else {
                    CommandExecutor::new_command(cmd, Box::new(self))
                }
            };

            let result = command_executor.execute().await?;

            let is_failed = result.failed();

            task_result.exit_code = result.exit_code;
            task_result.output = result.output;
            task_result.status = if is_failed {
                TaskStatus::Failed
            } else {
                TaskStatus::Success
            };

            if is_failed {
                break;
            }
        }

        Ok(task_result)
    }
}
