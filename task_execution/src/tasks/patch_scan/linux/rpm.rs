use std::collections::HashMap;

use crate::tasks::command_executor::CommandExecutor;
use crate::TaskExecutable;
use crate::{tasks::patch_scan::LinuxPatchScanner, TaskExecutionError};
use anyhow::anyhow;
use api::patch::send_patch_discovered_data;
use async_trait::async_trait;
use logger::{debug, error, info};
use serde::Deserialize;
use tokio::{fs, time::Instant};

const REPO_WRITE_PATH: &str = "/etc/yum.repos.d/zirozen-repo.repo";

#[derive(Deserialize, Debug)]
struct Command {
    class: String,
    query: String,
}

#[derive(Deserialize, Debug)]
struct CommandBlock {
    name: String,
    ssh: Vec<Command>,
}

#[derive(Deserialize, Debug)]
struct CommandBlocks {
    queries: Vec<CommandBlock>,
}

pub struct RpmPatchScanner<'a> {
    endpoint_id: i64,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> RpmPatchScanner<'a> {
    pub fn new(endpoint_id: i64, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self { endpoint_id, task }
    }

    async fn execute_command_blocks(
        &self,
        command_blocks: CommandBlocks,
    ) -> Result<HashMap<String, String>, TaskExecutionError> {
        debug!("Executing command blocks {:?}", command_blocks);
        let mut output = HashMap::new();

        for command_block in command_blocks.queries {
            info!("Executing command block {}", command_block.name);
            for command in command_block.ssh {
                info!("Executing command class {}", command.class);
                let cmd = match CommandExecutor::new_command(&command.query, Box::new(*self.task))
                    .capture()
                    .execute()
                    .await
                {
                    Ok(output) => {
                        info!(
                            "Command class {} is executed with exit code {}",
                            command.class, output.exit_code
                        );
                        if output.succeeded() {
                            self.task
                                .write_task_log(
                                    format!(
                                        "command class {} executed successfully.",
                                        command.class
                                    ),
                                    None,
                                )
                                .await;
                        } else {
                            self.task
                                .write_task_log(
                                    format!(
                                        "Failed to execute command class {} with exit code {}.",
                                        command.class, output.exit_code
                                    ),
                                    None,
                                )
                                .await;
                            return Err(anyhow!(
                                "Failed to execute command class {} with exit code {}",
                                command.class,
                                output.exit_code
                            )
                            .into());
                        }
                        output
                    }
                    Err(error) => {
                        error!(?error, "Failed to execute command {}", command.query);
                        self.task
                            .write_task_log(
                                format!(
                                    "Failed to execute command {} with error {:?}",
                                    command.query, error
                                ),
                                Some("ERROR"),
                            )
                            .await;
                        return Err(error.into());
                    }
                };
                output.insert(command.class, cmd.output);
            }
        }

        Ok(output)
    }
}

#[async_trait]
impl<'a> LinuxPatchScanner for RpmPatchScanner<'a> {
    async fn scan(&mut self) -> anyhow::Result<()> {
        let time = Instant::now();

        if self.task.get_task().custom_task_details.is_none() {
            error!("No custom task details found for {:?}", self.task);
            self.task
                .write_task_log("No custom task details found".to_owned(), Some("ERROR"))
                .await;
            return Err(anyhow!("No custom task details found for {:?}", self.task));
        }

        let custom_task_details = self
            .task
            .get_task()
            .custom_task_details
            .as_ref()
            .unwrap()
            .as_object()
            .unwrap();

        debug!(
            "Got custom task detail as. {}",
            serde_json::to_string_pretty(&custom_task_details)?
        );

        let repo_block = match custom_task_details.get("repo_block") {
            Some(repo_block) => repo_block.as_str().unwrap_or_default(),
            None => {
                error!(
                    "No repo block found in task details {:?}",
                    custom_task_details
                );
                self.task
                    .write_task_log(
                        format!(
                            "No repo block found in task details {:?}",
                            custom_task_details
                        ),
                        Some("ERROR"),
                    )
                    .await;
                return Err(anyhow!(
                    "No repo sync blocks found for {:?}",
                    custom_task_details
                ));
            }
        };

        fs::remove_file(REPO_WRITE_PATH).await.ok();

        match fs::write(REPO_WRITE_PATH, repo_block).await {
            Ok(_) => {
                self.task
                    .write_task_log(
                        format!("Repo block has been written to path {}", REPO_WRITE_PATH),
                        None,
                    )
                    .await;
                debug!(
                    "Wrote repo block {} to file {}",
                    repo_block, REPO_WRITE_PATH
                );
            }
            Err(error) => {
                error!(?error, "Failed to write repo block to {}", REPO_WRITE_PATH);
                self.task
                    .write_task_log(
                        format!(
                            "Failed to write repo block to {} with error {:?}",
                            REPO_WRITE_PATH, error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                return Err(error.into());
            }
        };

        let commands = match custom_task_details.get("command_block") {
            Some(commands) => commands,
            None => {
                error!(
                    "No commands found in task details {:?}",
                    custom_task_details
                );
                self.task
                    .write_task_log(
                        format!(
                            "No commands found in task details {:?}",
                            custom_task_details
                        ),
                        Some("ERROR"),
                    )
                    .await;
                return Err(anyhow!(
                    "No commands found in task details {:?}",
                    custom_task_details
                ));
            }
        };

        debug!("Command block: {:?}", commands);

        let command_blocks = match serde_json::from_value::<CommandBlocks>(commands.clone()) {
            Ok(config) => config,
            Err(error) => {
                error!(?error, "Failed to parse commands from task details");
                self.task
                    .write_task_log(
                        format!(
                            "Failed to parse commands from task details with error {:?}",
                            error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                return Err(error.into());
            }
        };

        let command_output = match self.execute_command_blocks(command_blocks).await {
            Ok(output) => {
                debug!("Got output from command blocks {:?}", output);
                self.task
                    .write_task_log("Executed all command blocks successfully.".to_owned(), None)
                    .await;
                output
            }
            Err(error) => {
                error!(?error, "Failed to execute command blocks");
                self.task
                    .write_task_log(
                        format!("Failed to execute command blocks with error {:?}", error),
                        Some("ERROR"),
                    )
                    .await;
                return Err(error.into());
            }
        };

        debug!(
            "Command Output: {}",
            serde_json::to_string_pretty(&command_output)?
        );

        send_patch_discovered_data(
            self.endpoint_id,
            serde_json::json!({
                "patchData": command_output,
            }),
        )
        .await?;

        debug!("Time taken for full scanning {:?}", time.elapsed());

        Ok(())
    }
}
