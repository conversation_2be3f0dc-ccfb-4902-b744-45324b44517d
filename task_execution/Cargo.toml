[package]
name = "task_execution"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
agent_manager = { path = "../agent_manager" }
data_collection = { path = "../data_collection" }
database = { path = "../database" }
shell = { path = "../shell" }
api = { path = "../api" }
utils = { path = "../utils" }
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
tokio = { version = "1.46.1", features = ["full", "tracing"] }
chrono = "0.4.41"
evalexpr = "12.0.2"
async_zip = { version = "0.0.17", features = [
  "full",
  "tokio",
  "tokio-fs",
  "tokio-util",
] }
tokio-tar = "0.3.1"
tokio-stream = "0.1.17"
tokio-util = "0.7.15"
sanitize-filename = "0.6.0"
sevenz-rust = "0.6.1"
csv-async = { version = "1.3.1", features = ["tokio"] }
base64 = "0.22.1"
sysinfo = "0.36.0"
async-trait = "0.1.88"
cfg-if = "1.0.1"
futures-util = "0.3.31"
async-speed-limit = { version = "0.4.2", features = ["tokio"] }
self-replace = "=1.5.0"
rayon = "1.10.0"
configparser = { version = "3.1.0", features = ["async-std", 'tokio'] }
version-compare = "0.2.0"

[target.'cfg(windows)'.dependencies]
windows_registry = { path = "../windows_registry" }
windows_patch_xml_checker = { path = "../windows_patch_xml_checker" }
windows = { version = "0.61.3", features = [
  "Win32_UI_Shell",
  "Win32_Storage_FileSystem",
  "Win32_Foundation",
  "Win32_System",
  "Win32_System_Com",
  "Win32_System_UpdateAgent",
] }
dashmap = { version = "6.1.0", features = ["rayon"] }
encoding_rs_io = "0.1.7"
encoding_rs = "0.8.35"

[target.'cfg(target_os = "linux")'.dependencies]
os_info = "3.12.0"
deb-version = "0.1.1"
