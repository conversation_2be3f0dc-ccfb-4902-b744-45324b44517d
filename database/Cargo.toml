[package]
name = "database"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
anyhow = { version = "1.0.98", features = ["backtrace"] }
serde = { version = "1.0.219", features = ["alloc", "derive", "rc"] }
serde_json = "1.0.140"
surrealdb = { version = "2.3.7", features = ["kv-rocksdb"] }
thiserror = "2.0.12"
tokio = { version = "1.46.1", features = ["full", "tracing"] }
serde_repr = "0.1.20"
serde_with = "3.14.0"
chrono = "0.4.41"
sysinfo = "0.36.0"
async-trait = "0.1.88"
hex = "0.4.3"
sha1 = "0.10.6"
sha2 = "0.10.9"
md-5 = "0.10.6"
