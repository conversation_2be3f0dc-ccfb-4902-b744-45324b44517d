use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ey};
use serde::{Deserialize, Serialize};
use std::fmt::Debug;
use surrealdb::Uuid;
use sysinfo::{Networks, System};

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, De<PERSON>ult, PartialEq, Eq)]
pub enum EnrollmentStatus {
    #[serde(alias = "pending")]
    Pending,
    #[serde(alias = "rejected")]
    Rejected,
    #[serde(alias = "approved")]
    Approved,
    #[default]
    Unknown,
}

#[derive(Serialize, Deserialize, Clone, Default)]
pub struct ServerConfig {
    id: PrimaryKey,
    url: String,
    uuid: String,
    enroll_id: i64,
    enroll_secret: String,
    status: EnrollmentStatus,
    host_name: String,
    ip_addresses: Vec<String>,
    username: String,
    password: String,
    created_at: i64,
}

impl Debug for ServerConfig {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ServerConfig")
            .field("url", &self.url)
            .field("uuid", &self.uuid)
            .field("host", &self.host_name)
            .field("ip", &self.ip_addresses)
            .field("status", &self.status)
            .field("enroll_id", &self.enroll_id)
            .field("created_at", &self.created_at)
            .finish()
    }
}

impl ServerConfig {
    pub fn new(url: String, enroll_id: i64, uuid: Option<String>) -> Self {
        let networks = Networks::new_with_refreshed_list();
        let ip_addresses = networks
            .into_iter()
            .flat_map(|item| {
                item.1
                    .ip_networks()
                    .iter()
                    .filter(|item| item.addr.is_ipv4() && !item.addr.is_loopback())
                    .map(|address| address.addr.to_string())
            })
            .collect();

        Self {
            url,
            enroll_id,
            uuid: uuid.unwrap_or_else(|| Uuid::new_v4().to_string()),
            host_name: System::host_name().unwrap_or("Unknown".to_owned()),
            ip_addresses,
            created_at: chrono::Utc::now().timestamp(),
            ..Default::default()
        }
    }

    pub fn created_at(&self) -> i64 {
        self.created_at
    }

    pub fn set_created_at(mut self) -> Self {
        self.created_at = chrono::Utc::now().timestamp();

        self
    }

    pub fn set_enroll_secret(mut self, enroll_secret: String) -> Self {
        self.enroll_secret = enroll_secret;

        self
    }

    pub fn set_id(mut self, id: PrimaryKey) -> Self {
        self.id = id;

        self
    }

    pub fn set_username(mut self, username: String) -> Self {
        self.username = username;

        self
    }

    pub fn set_status(mut self, status: EnrollmentStatus) -> Self {
        self.status = status;

        self
    }

    pub fn set_password(mut self, password: String) -> Self {
        self.password = password;

        self
    }

    pub fn is_approved(&self) -> bool {
        match self.status {
            EnrollmentStatus::Pending => false,
            EnrollmentStatus::Rejected => false,
            EnrollmentStatus::Approved => true,
            EnrollmentStatus::Unknown => false,
        }
    }

    pub fn is_rejected(&self) -> bool {
        match self.status {
            EnrollmentStatus::Pending => false,
            EnrollmentStatus::Rejected => true,
            EnrollmentStatus::Approved => false,
            EnrollmentStatus::Unknown => false,
        }
    }

    pub fn is_pending(&self) -> bool {
        match self.status {
            EnrollmentStatus::Pending => true,
            EnrollmentStatus::Rejected => false,
            EnrollmentStatus::Approved => false,
            EnrollmentStatus::Unknown => false,
        }
    }

    pub fn url(&self) -> &str {
        &self.url
    }

    pub fn uuid(&self) -> &str {
        &self.uuid
    }

    pub fn host(&self) -> &str {
        &self.host_name
    }

    pub fn ip_addresses(&self) -> &Vec<String> {
        &self.ip_addresses
    }

    pub fn status(&self) -> &EnrollmentStatus {
        &self.status
    }

    pub fn username(&self) -> &str {
        &self.username
    }

    pub fn password(&self) -> &str {
        &self.password
    }

    pub fn enroll_id(&self) -> i64 {
        self.enroll_id
    }

    pub fn enroll_secret(&self) -> &str {
        &self.enroll_secret
    }
}

impl HasPrimaryKey for ServerConfig {
    fn table_name(&self) -> &str {
        "server_config"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for ServerConfig {}
