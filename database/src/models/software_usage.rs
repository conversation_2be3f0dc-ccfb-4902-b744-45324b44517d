use serde::{Deserialize, Serialize};

use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>};

#[derive(Debug, Serialize, Deserialize, Hash, PartialEq, Eq, <PERSON><PERSON>, Default)]
pub struct SoftwareUsage {
    pub id: PrimaryKey,
    pub software_id: i64,
    pub pid: u32,
    pub file_name: String,
    pub start_time: i64,
    pub elapsed_time: i64,
    pub username: String,
    pub uid: String,
    pub end_time: Option<i64>,
}

impl SoftwareUsage {
    pub fn with_id(mut self, id: i64) -> Self {
        self.id = id.into();

        self
    }
}

impl HasPrimaryKey for SoftwareUsage {
    fn table_name(&self) -> &str {
        "software_usage"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for SoftwareUsage {}
