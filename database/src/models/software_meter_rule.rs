use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>h, <PERSON>ialEq, Eq, <PERSON><PERSON>, Default)]
pub struct SoftwareMeterRule {
    id: PrimaryK<PERSON>,
    software_id: i64,
    file_name: String,
}

impl SoftwareMeterRule {
    pub fn id(&self) -> i64 {
        return self.id.to_i64();
    }

    pub fn software_id(&self) -> i64 {
        self.software_id
    }

    pub fn file_name(&self) -> &str {
        &self.file_name
    }
}

impl HasPrimaryKey for SoftwareMeterRule {
    fn table_name(&self) -> &str {
        "software_meter_rules"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for SoftwareMeterRule {}
