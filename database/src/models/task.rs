use super::{
    ComplianceRule, Configuration, Deployment, DeploymentInitiation, DeploymentPolicy,
    DeploymentPolicyType, Package, Patch, QuickCheck,
};
use crate::{
    models::RedhatSyncBlocks,
    queries::{build_pending_task_query, build_reboot_required_task_query},
    Has<PERSON><PERSON>ry<PERSON>ey, Model, PrimaryKey, DB,
};
use logger::{debug, error};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{collections::HashMap, fmt::Display};

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum TaskType {
    #[serde(rename = "package")]
    Package,
    #[serde(rename = "patch", alias = "Patch")]
    Patch,
    #[serde(rename = "configuration")]
    Configuration,
    #[serde(rename = "compliances")]
    Compliance,
    #[serde(rename = "deployment")]
    Deployment,
    #[serde(rename = "patch_scan")]
    PatchScan,
    #[serde(rename = "query_execution")]
    QueryExecution,
    #[serde(rename = "system_action")]
    SystemAction,
    #[serde(rename = "system_actions")]
    SystemActions,
    #[serde(rename = "application_control_policy")]
    ApplicationControlPolicy,
    #[serde(rename = "application_control_policies")]
    ApplicationControlPolicies,
    #[serde(rename = "compliance_test")]
    ComplianceTest,
    #[serde(rename = "quick_check_test")]
    QuickCheck,
    #[serde(rename = "upgrade")]
    Upgrade,
    #[serde(rename = "redhat_agent_nomination")]
    RedhatAgentNomination,
    #[serde(rename = "")]
    #[default]
    None,
}

impl Display for TaskType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskType::None => f.write_str(""),
            TaskType::Package => f.write_str("package"),
            TaskType::Patch => f.write_str("patch"),
            TaskType::Configuration => f.write_str("configuration"),
            TaskType::Compliance => f.write_str("compliance"),
            TaskType::SystemAction => f.write_str("system_action"),
            TaskType::SystemActions => f.write_str("system_actions"),
            TaskType::ApplicationControlPolicy => f.write_str("application_control_policy"),
            TaskType::ApplicationControlPolicies => f.write_str("application_control_policies"),
            TaskType::Deployment => f.write_str("deployment"),
            TaskType::PatchScan => f.write_str("patch_scan"),
            TaskType::QueryExecution => f.write_str("query_execution"),
            TaskType::ComplianceTest => f.write_str("compliance_test"),
            TaskType::QuickCheck => f.write_str("quick_check"),
            TaskType::Upgrade => f.write_str("upgrade"),
            TaskType::RedhatAgentNomination => f.write_str("redhat_agent_nomination"),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum TaskStatus {
    #[serde(rename = "waiting")]
    #[default]
    Waiting,
    #[serde(rename = "initiated")]
    Initiated,
    #[serde(rename = "in_progress")]
    InProgress,
    #[serde(rename = "success")]
    Success,
    #[serde(rename = "reboot_required")]
    SuccessWithRebootRequired,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "cancelled")]
    Cancelled,
    #[serde(rename = "ready_to_deploy")]
    ReadyToDeploy,
}

impl Display for TaskStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskStatus::Waiting => f.write_str("waiting"),
            TaskStatus::Initiated => f.write_str("initiated"),
            TaskStatus::InProgress => f.write_str("in_progress"),
            TaskStatus::Success => f.write_str("success"),
            TaskStatus::SuccessWithRebootRequired => f.write_str("reboot_required"),
            TaskStatus::Failed => f.write_str("failed"),
            TaskStatus::Cancelled => f.write_str("cancelled"),
            TaskStatus::ReadyToDeploy => f.write_str("ready_to_deploy"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Default, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Task {
    pub id: PrimaryKey,
    pub name: Option<String>,
    pub display_name: Option<String>,
    #[serde(alias = "refModel", alias = "ref_model")]
    pub module_name: Option<TaskType>,
    #[serde(alias = "refId", alias = "ref_id")]
    pub resource_id: i32,
    #[serde(alias = "taskStatus", alias = "task_status")]
    pub status: TaskStatus,
    pub deployment_id: i32,
    pub agent_id: i32,
    #[serde(alias = "taskResult", alias = "task_result")]
    pub result: Option<String>,
    pub retry_count: Option<i32>,
    #[serde(alias = "task_type", alias = "taskType")]
    pub task_type: Option<TaskType>,
    pub custom_task_details: Option<Value>,
    pub exit_code: Option<i32>,
    pub is_finished: Option<bool>,
    pub deployment: Option<Deployment>,
    pub package: Option<Package>,
    pub patch: Option<Patch>,
    pub configuration: Option<Configuration>,
    pub compliance: Option<ComplianceRule>,
    pub policy: Option<DeploymentPolicy>,
    pub commands: Option<Value>,
    pub quick_check: Option<QuickCheck>,
    pub redhat_sync_blocks: Option<RedhatSyncBlocks>,
}

impl Task {
    pub fn should_put_in_primary_queue(&self) -> bool {
        self.is_system_action_task()
            || self.is_upgrade_task()
            || self.is_compliance_task()
            || self.is_live_test_task()
            || self.is_application_control_policy()
    }

    pub async fn change_status(mut self, status: TaskStatus) -> Task {
        self.status = status.clone();
        match self.persist().await {
            Ok(task) => task,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to change status of task to {} in db", status
                );
                self
            }
        }
    }

    pub fn should_abort(&self) -> bool {
        self.status != TaskStatus::Initiated && self.status != TaskStatus::InProgress
    }

    pub async fn get_reboot_required_tasks() -> Vec<Task> {
        let query = build_reboot_required_task_query();

        debug!("Query to get success with reboot tasks: {}", query);

        Task::execute_query(query).await
    }

    async fn execute_query(query: String) -> Vec<Task> {
        match DB.query(query).await {
            Ok(mut response) => match response.take(0) {
                Ok(tasks) => tasks,
                Err(error) => {
                    error!(?error, "Failed to unwrap response from database");
                    vec![]
                }
            },
            Err(error) => {
                error!(?error, "Failed to get scheduled task from database");
                vec![]
            }
        }
    }

    pub async fn get_pending_tasks() -> Vec<Task> {
        let query = build_pending_task_query();

        debug!("Query to get pending jobs: {}", query);

        Task::execute_query(query).await
    }

    pub fn is_patch_scan_task(&self) -> bool {
        self.task_type == Some(TaskType::PatchScan)
    }

    pub fn is_deployment_task(&self) -> bool {
        let task_type = if self
            .task_type
            .as_ref()
            .is_some_and(|item| item == &TaskType::Deployment)
        {
            self.module_name.as_ref().unwrap()
        } else {
            self.task_type.as_ref().unwrap()
        };
        task_type == &TaskType::Patch || task_type == &TaskType::Package
    }

    pub fn should_persist(&self) -> bool {
        self.is_system_action_task() == false
            && self.is_upgrade_task() == false
            && self.is_live_test_task() == false
            && self.is_application_control_policy() == false
    }

    pub fn is_upgrade_task(&self) -> bool {
        self.task_type
            .as_ref()
            .is_some_and(|task_type| task_type == &TaskType::Upgrade)
    }

    pub fn is_system_action_task(&self) -> bool {
        self.task_type.as_ref().is_some_and(|task_type| {
            task_type == &TaskType::SystemAction
                || task_type == &TaskType::SystemActions
                || task_type == &TaskType::QuickCheck
        })
    }

    pub fn is_recurring_task(&self) -> bool {
        self.policy.as_ref().is_some_and(|policy| {
            policy
                .initiate_deployment_on
                .as_ref()
                .is_some_and(|i| i == &DeploymentInitiation::Recurring)
        })
    }

    pub fn is_scheduled_task(&self) -> bool {
        self.policy.as_ref().is_some_and(|policy| {
            policy.policy_type == DeploymentPolicyType::Scheduled
                && policy
                    .initiate_deployment_on
                    .as_ref()
                    .is_some_and(|i| i != &DeploymentInitiation::Recurring)
        })
    }

    pub fn remaining_attempts(&self) -> i32 {
        if self.is_upgrade_task()
            || self.is_system_action_task()
            || self.is_live_test_task()
            || self.is_recurring_task()
        {
            return 1;
        }
        if self.retry_count.is_some() {
            self.retry_count.as_ref().unwrap().to_owned()
        } else {
            1
        }
    }

    pub fn has_all_attempt_exceeded(&self) -> bool {
        if self.is_upgrade_task()
            || self.is_system_action_task()
            || self.is_live_test_task()
            || self.is_recurring_task()
        {
            return true;
        }
        if self.retry_count.is_some() {
            return self.retry_count.as_ref().unwrap().to_owned() <= 0;
        } else {
            return true;
        }
    }

    pub fn get_current_attempt_number(&self) -> i32 {
        if self.is_upgrade_task()
            || self.is_system_action_task()
            || self.is_live_test_task()
            || self.is_recurring_task()
        {
            return 1;
        }
        if self.retry_count.is_some() {
            if let Some(d) = self.deployment.as_ref() {
                d.retry_count - self.retry_count.as_ref().unwrap()
            } else {
                self.retry_count.as_ref().unwrap().to_owned()
            }
        } else {
            1
        }
    }

    pub fn is_live_test_task(&self) -> bool {
        self.task_type
            .as_ref()
            .is_some_and(|task_type| task_type == &TaskType::ComplianceTest)
    }

    pub fn is_application_control_policy(&self) -> bool {
        self.task_type.as_ref().is_some_and(|task_type| {
            task_type == &TaskType::ApplicationControlPolicy
                || task_type == &TaskType::ApplicationControlPolicies
        })
    }

    pub fn is_compliance_task(&self) -> bool {
        self.task_type
            .as_ref()
            .is_some_and(|task_type| task_type == &TaskType::Compliance)
    }

    pub fn is_persisted(&self) -> bool {
        self.id.is_persisted()
    }

    pub fn is_instant_deployment(&self) -> bool {
        self.deployment.is_none()
            || self
                .policy
                .as_ref()
                .is_some_and(|policy| policy.is_instant())
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Command {
    pub command: String,
    pub command_type: String,
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct SystemActionCommand {
    pub command: Vec<Command>,
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct ApplicationControlPolicyCommand {
    pub context: String,
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PatchSystemScanCommands {
    pub cmd: Option<HashMap<String, String>>,
    pub ps: Option<HashMap<String, String>>,
    pub reg: Option<HashMap<String, String>>,
    pub wmi: Option<HashMap<String, String>>,
}

impl From<Value> for SystemActionCommand {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into SystemActionCommand");
                SystemActionCommand::default()
            }
        }
    }
}

impl From<&Value> for SystemActionCommand {
    fn from(value: &Value) -> Self {
        match serde_json::from_value::<Self>(value.to_owned()) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into SystemActionCommand");
                SystemActionCommand::default()
            }
        }
    }
}

impl From<Value> for ApplicationControlPolicyCommand {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to convert Value into ApplicationControlPolicyCommand"
                );
                ApplicationControlPolicyCommand::default()
            }
        }
    }
}

impl From<&Value> for ApplicationControlPolicyCommand {
    fn from(value: &Value) -> Self {
        match serde_json::from_value::<Self>(value.to_owned()) {
            Ok(data) => data,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to convert Value into ApplicationControlPolicyCommand"
                );
                ApplicationControlPolicyCommand::default()
            }
        }
    }
}

impl From<Value> for PatchSystemScanCommands {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to convert Value into PatchScanScrapCommands"
                );
                PatchSystemScanCommands::default()
            }
        }
    }
}

impl From<&Value> for PatchSystemScanCommands {
    fn from(value: &Value) -> Self {
        match serde_json::from_value::<Self>(value.to_owned()) {
            Ok(data) => data,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to convert Value into PatchScanScrapCommands"
                );
                PatchSystemScanCommands::default()
            }
        }
    }
}

impl HasPrimaryKey for Task {
    fn table_name(&self) -> &str {
        "tasks"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for Task {}
