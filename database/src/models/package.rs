use super::FileAttachment;
use crate::PrimaryKey;
use serde::{Deserialize, Serialize};
use std::fmt::Display;
use utils::serde::none_if_empty_value;

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug)]
pub enum PackageType {
    #[serde(rename = "script")]
    Script,
    #[serde(rename = "application")]
    Application,
    #[serde(rename = "msi")]
    Msi,
    #[serde(rename = "exe")]
    Exe,
    #[serde(rename = "zip")]
    Zip,
}

impl Display for PackageType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PackageType::Script => f.write_str("script"),
            PackageType::Application => f.write_str("application"),
            PackageType::Msi => f.write_str("msi"),
            PackageType::Exe => f.write_str("exe"),
            PackageType::Zip => f.write_str("zip"),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug)]
pub enum PackageLocation {
    #[serde(rename = "local_dir")]
    LocalDir,
    #[serde(rename = "shared_dir")]
    SharedDir,
    #[serde(rename = "public_url")]
    PublicURL,
}

impl Display for PackageLocation {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PackageLocation::LocalDir => f.write_str("local_dir"),
            PackageLocation::SharedDir => f.write_str("shared_dir"),
            PackageLocation::PublicURL => f.write_str("public_url"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Package {
    pub id: PrimaryKey,
    pub name: Option<String>,
    pub display_name: Option<String>,
    pub pkg_file_path: FileAttachment,
    pub pkg_type: PackageType,
    pub pkg_location: PackageLocation,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub install_command: Option<String>,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub uninstall_command: Option<String>,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub upgrade_command: Option<String>,
    pub icon_file: Option<FileAttachment>,
    pub self_service_supported: Option<bool>,
}
