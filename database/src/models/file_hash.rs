use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DB};
use chrono::{DateTime, Local};
use logger::{debug, error, trace};
use md5::{Digest as Md5Digest, Md5};
use serde::{Deserialize, Serialize};
use sha1::Sha1;
use sha2::Sha256;
use std::{path::PathBuf, time::Instant};
use tokio::{
    fs::File,
    io::{AsyncReadExt, BufReader},
};
use utils::constants::MAX_FILE_SIZE_FOR_HASHING;

#[derive(Debug, Default, Serialize, Deserialize, Clone)]
pub struct FileHash {
    md5: String,
    sha1: String,
    sha256: String,
    file_path: PrimaryKey,
    file_size: u64,
    last_modified: i64,
}

impl FileHash {
    pub fn new<T: AsRef<str>>(file_path: T) -> Self {
        FileHash {
            file_path: file_path.as_ref().into(),
            ..Default::default()
        }
    }

    pub async fn generate_and_persist(&self) -> Self {
        FileHash::generate_hashes_and_persist(self.file_path.to_string().as_str()).await
    }
}

impl FileHash {
    pub fn file_path(&self) -> String {
        self.file_path.to_string()
    }

    pub fn md5(&self) -> &str {
        &self.md5
    }

    pub fn sha1(&self) -> &str {
        &self.sha1
    }

    pub fn sha256(&self) -> &str {
        &self.sha256
    }

    pub async fn for_path(path: String) -> Option<FileHash> {
        let hash = FileHash {
            file_path: path.into(),
            ..Default::default()
        };

        match hash.load().await {
            Ok(hash) => Some(hash),
            Err(_) => None,
        }
    }

    pub async fn for_paths(paths: Vec<String>) -> Vec<FileHash> {
        match DB
            .query(format!(
                "select * from {} where file_path in $paths",
                Self::default().table_name()
            ))
            .bind(("paths", paths.join(",")))
            .await
        {
            Ok(mut value) => value.take(0).unwrap_or_default(),
            Err(error) => {
                error!(?error, "Failed to get all file_hahses");
                vec![]
            }
        }
    }

    pub async fn generate_hashes(file_path: &str) -> Option<FileHash> {
        if file_path.is_empty() {
            return None;
        }
        trace!("Generating hash for file {}", file_path);
        let file_path_buf = PathBuf::from(file_path);
        if file_path_buf.exists() == false {
            return None;
        }

        let time = Instant::now();
        let mut buffer = [0u8; 8192];
        let mut fs_hash = FileHash::default();

        let mut file = match File::open(file_path).await {
            Ok(f) => {
                match f.metadata().await {
                    Ok(metadata) => {
                        if metadata.len() > MAX_FILE_SIZE_FOR_HASHING {
                            error!("File size is greater than {:.2}MB at path {}, skipping hash computation actual size is {}", MAX_FILE_SIZE_FOR_HASHING / (1024 * 1024), file_path, metadata.len());
                            return None;
                        }
                        fs_hash.file_path = file_path.to_owned().into();
                        fs_hash.file_size = metadata.len();
                        fs_hash.last_modified =
                            Into::<DateTime<Local>>::into(metadata.modified().unwrap()).timestamp();
                    }
                    _ => {}
                }
                BufReader::new(f)
            }
            Err(error) => {
                error!(?error, "Error opening file: {}", file_path);
                return None;
            }
        };

        let mut md5_hasher = Md5::new();
        let mut sha1_hasher = Sha1::new();
        let mut sha256_hasher = Sha256::new();

        while let Ok(bytes_read) = file.read(&mut buffer[..]).await {
            if bytes_read == 0 {
                break;
            }

            md5_hasher.update(&buffer[..bytes_read]);
            sha1_hasher.update(&buffer[..bytes_read]);
            sha256_hasher.update(&buffer[..bytes_read]);
        }
        fs_hash.md5 = hex::encode(md5_hasher.finalize());
        fs_hash.sha1 = hex::encode(sha1_hasher.finalize());
        fs_hash.sha256 = hex::encode(sha256_hasher.finalize());

        trace!(
            "Took {:?} time to compute hash for file {}",
            time.elapsed(),
            file_path
        );
        Some(fs_hash)
    }

    pub async fn generate_hashes_and_persist(file_path: &str) -> FileHash {
        let file_hash = FileHash {
            file_path: file_path.to_string().into(),
            ..Default::default()
        };

        match file_hash.clone().load().await {
            Ok(persisted_file_hash) => {
                debug!("Found file_hash in db checking metadata for changes");
                match File::open(file_path).await {
                    Ok(f) => match f.metadata().await {
                        Ok(metadata) => {
                            let current_last_modified =
                                Into::<DateTime<Local>>::into(metadata.modified().unwrap())
                                    .timestamp();

                            if current_last_modified > persisted_file_hash.last_modified {
                                debug!(
                                    "File {} is not changed so using last hash value",
                                    file_path
                                );
                                return persisted_file_hash;
                            }
                            debug!("File {} is changed so recomputing hash", file_path);
                        }
                        Err(error) => {
                            error!(?error, "Failed to read metadata of file {}", file_path);
                            return file_hash;
                        }
                    },
                    Err(error) => {
                        error!(?error, "Failed to open file {}", file_path);
                        return file_hash;
                    }
                };
            }
            _ => {}
        };

        match FileHash::generate_hashes(file_path).await {
            Some(fs_hash) => match fs_hash.persist().await {
                Ok(persisted_file_hash) => persisted_file_hash,
                Err(_) => file_hash,
            },
            None => file_hash,
        }
    }
}

impl HasPrimaryKey for FileHash {
    fn table_name(&self) -> &str {
        return "file_hash";
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.file_path
    }
}

impl Model for FileHash {}
