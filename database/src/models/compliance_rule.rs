use super::{CommandType, FileAttachment};
use crate::DatabaseError;
use crate::PrimaryKey;
use logger::debug;
use logger::error;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use serde_with::serde_as;

#[derive(Serialize, Deserialize, Debug, Clone, De<PERSON>ult, PartialEq, Eq, Hash)]
pub enum RuleOperator {
    #[default]
    Equal,
    NotEqual,
    GreaterThan,
    LessThan,
    #[serde(alias = "GreaterThanEqual")]
    GreaterThanOrEqualTo,
    #[serde(alias = "LessThanEqual")]
    LessThanOrEqualTo,
    Contains,
    #[serde(alias = "StartWith")]
    StartsWith,
    #[serde(alias = "EndWith")]
    EndsWith,
    #[serde(alias = "NotContains")]
    DoesNotContains,
}

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, <PERSON><PERSON>ult, <PERSON>h, <PERSON>ialEq, Eq)]
pub enum LogicalCondition {
    #[serde(rename = "and", alias = "AND")]
    #[default]
    And,
    #[serde(rename = "or", alias = "OR")]
    Or,
}

impl LogicalCondition {
    pub fn expression(&self) -> &str {
        match self {
            LogicalCondition::And => "&&",
            LogicalCondition::Or => "||",
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq, Eq)]
#[serde(rename_all = "camelCase")]
pub struct OutputMatch {
    #[serde(rename = "conditionValue", alias = "ConditionValue")]
    pub value: String,
    #[serde(rename = "condition", alias = "Condition")]
    pub group_condition: Option<LogicalCondition>,
    #[serde(rename = "ruleCondition", alias = "RuleCondition")]
    pub operator: RuleOperator,
}

impl OutputMatch {
    pub fn get_expression(&self, output: &str) -> String {
        match self.operator {
            RuleOperator::Equal => {
                format!("{} {} {}", output.trim(), "==", self.value.as_str().trim())
            }
            RuleOperator::NotEqual => {
                format!("{} {} {}", output.trim(), "!=", self.value.as_str().trim())
            }
            RuleOperator::GreaterThan => {
                format!("{} {} {}", output.trim(), ">", self.value.as_str().trim())
            }
            RuleOperator::LessThan => {
                format!("{} {} {}", output.trim(), "<", self.value.as_str().trim())
            }
            RuleOperator::GreaterThanOrEqualTo => {
                format!("{} {} {}", output.trim(), ">=", self.value.as_str().trim())
            }
            RuleOperator::LessThanOrEqualTo => {
                format!("{} {} {}", output.trim(), "<=", self.value.as_str().trim())
            }
            RuleOperator::Contains => {
                format!(
                    "{} {} {}",
                    output.trim(),
                    "Contains",
                    self.value.as_str().trim()
                )
            }
            RuleOperator::StartsWith => {
                format!(
                    "{} {} {}",
                    output.trim(),
                    "Starts With",
                    self.value.as_str().trim()
                )
            }
            RuleOperator::EndsWith => {
                format!(
                    "{} {} {}",
                    output.trim(),
                    "Ends With",
                    self.value.as_str().trim()
                )
            }
            RuleOperator::DoesNotContains => {
                format!(
                    "{} {} {}",
                    output.trim(),
                    "not contains",
                    self.value.as_str().trim()
                )
            }
        }
    }

    pub fn is_passed(&self, output: &str) -> Result<bool, DatabaseError> {
        match self.operator {
            RuleOperator::Equal => {
                let result = output.trim() == self.value.trim();
                debug!("Output == with result {}", result);
                Ok(result)
            }
            RuleOperator::NotEqual => {
                let result = output.trim() != self.value.trim();
                debug!("Output != with result {}", result);
                Ok(result)
            }
            RuleOperator::GreaterThan => {
                let result = output.parse::<i64>()? > self.value.as_str().trim().parse::<i64>()?;
                debug!("Output > with result {}", result);
                Ok(result)
            }
            RuleOperator::LessThan => {
                let result = output.parse::<i64>()? < self.value.as_str().trim().parse::<i64>()?;
                debug!("Output < with result {}", result);
                Ok(result)
            }
            RuleOperator::GreaterThanOrEqualTo => {
                let result = output.parse::<i64>()? >= self.value.as_str().trim().parse::<i64>()?;
                debug!("Output <= with result {}", result);
                Ok(result)
            }
            RuleOperator::LessThanOrEqualTo => {
                let result = output.parse::<i64>()? <= self.value.as_str().trim().parse::<i64>()?;
                debug!("Output <= with result {}", result);
                Ok(result)
            }
            RuleOperator::Contains => {
                let result = output.contains(self.value.as_str().trim());
                debug!("Output contains result {}", result);
                Ok(result)
            }
            RuleOperator::StartsWith => {
                let result = output.starts_with(self.value.as_str().trim());
                debug!("Output starts with result {}", result);
                Ok(result)
            }
            RuleOperator::EndsWith => {
                let result = output.ends_with(self.value.as_str().trim());
                debug!("Output ends with result {}", result);
                Ok(result)
            }
            RuleOperator::DoesNotContains => {
                let result = !output.contains(self.value.as_str().trim());
                debug!("Output doesn't contains result {}", result);
                Ok(result)
            }
        }
    }
}

#[serde_as]
#[derive(Serialize, Deserialize, Clone, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct ComplianceRuleAction {
    pub command_type: Option<CommandType>,
    pub command: Option<String>,
    pub key: Option<String>,
    #[serde(rename = "complianceRuleConditions")]
    pub output_matches: Option<Vec<OutputMatch>>,
    pub condition: Option<LogicalCondition>,
    pub script_file: Option<FileAttachment>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
#[serde(rename_all = "camelCase")]
pub struct ComplianceRule {
    pub id: PrimaryKey,
    pub display_name: Option<String>,
    pub bindings: Option<String>,
    pub rule_type: Option<String>,
    pub status: Option<i32>,
    pub audit: Option<String>,
    pub execution_type: Option<String>,
    pub impact: Option<String>,
    pub remediation: Option<String>,
    pub rules: Vec<ComplianceRuleAction>,
}

impl From<Value> for ComplianceRule {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into ComplianceRule");
                ComplianceRule::default()
            }
        }
    }
}

impl From<&Value> for ComplianceRule {
    fn from(value: &Value) -> Self {
        match serde_json::from_value::<Self>(value.to_owned()) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into ComplianceRule");
                ComplianceRule::default()
            }
        }
    }
}
