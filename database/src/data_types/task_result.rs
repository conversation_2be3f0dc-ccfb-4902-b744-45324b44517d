use crate::models::TaskStatus;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Default, <PERSON><PERSON>)]
pub struct TaskResult {
    pub status: TaskStatus,
    pub output: String,
    pub exit_code: i32,
}

impl TaskResult {
    pub fn is_succeeded(&self) -> bool {
        self.status == TaskStatus::Success || self.status == TaskStatus::SuccessWithRebootRequired
    }

    pub fn is_failed(&self) -> bool {
        self.status == TaskStatus::Failed
    }
}
