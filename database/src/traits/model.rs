use super::<PERSON><PERSON><PERSON>;
use crate::{DatabaseError, DB};
use async_trait::async_trait;
use logger::{error, trace};
use serde::{de::DeserializeOwned, Serialize};
use std::collections::HashSet;
use std::fmt::Debug;
use std::str::FromStr;
use surrealdb::{engine::local::Db, RecordId, Surreal};

pub trait HasPrimaryKey {
    fn table_name(&self) -> &str;

    fn get_primary_key(&self) -> &PrimaryKey;
}

#[async_trait]
pub trait Model: HasPrimaryKey + Debug + Clone + Sized + Serialize + DeserializeOwned
where
    Self: Send + Sync + 'static,
{
    fn is_persisted(&self) -> bool {
        self.get_primary_key().is_persisted()
    }

    async fn raw_query(self, query: String) -> Result<Vec<Self>, DatabaseError> {
        match DB.query(query).await {
            Ok(mut response) => match response.take::<Vec<Self>>(0) {
                Ok(data) => Ok(data),
                Err(error) => {
                    error!(?error, "Failed to get query response");
                    return Err(error.into());
                }
            },
            Err(error) => {
                error!(?error, "Failed to get query result from database");
                return Err(error.into());
            }
        }
    }

    async fn delete_all(self) -> Result<Vec<Self>, DatabaseError> {
        Ok(DB.delete(self.table_name().to_string()).await?)
    }

    async fn delete_selected(self, items: HashSet<Self>) -> Result<Vec<Self>, DatabaseError> {
        let mut deleted_records = vec![];
        for record in items {
            let id = record.get_primary_key().to_string();
            match record.delete().await {
                Ok(record) => {
                    trace!("Record {:?} deleted successfully", record);
                    deleted_records.push(record);
                }
                Err(error) => {
                    error!(?error, "Failed to delete record with id {}", id);
                }
            }
        }
        Ok(deleted_records)
    }

    async fn get_all(self, limit: Option<u32>) -> Result<Vec<Self>, DatabaseError> {
        if limit.is_none() {
            Ok(DB.select(self.table_name().to_string()).await?)
        } else {
            match DB
                .query(format!("SELECT * from {} limit $limit", self.table_name()))
                .bind(("limit", limit.unwrap()))
                .await
            {
                Ok(mut response) => match response.take::<Vec<Self>>(0) {
                    Ok(data) => Ok(data),
                    Err(error) => {
                        error!(?error, "Failed to get query response");
                        return Err(error.into());
                    }
                },
                Err(error) => {
                    error!(?error, "Failed to get result from database");
                    return Err(error.into());
                }
            }
        }
    }

    async fn bulk_insert(self, data: HashSet<Self>) -> Vec<Self> {
        match DB
            .query(format!("INSERT INTO {} ($data);", self.table_name()))
            .bind(("data", data))
            .await
        {
            Ok(mut response) => match response.take::<Vec<Self>>(0) {
                Ok(data) => data,
                Err(error) => {
                    error!(?error, "Failed to get query response");
                    vec![]
                }
            },
            Err(error) => {
                error!(?error, "Failed to bulk insert into database");
                vec![]
            }
        }
    }

    async fn load(self) -> Result<Self, DatabaseError> {
        let record_id: RecordId = match self.get_primary_key() {
            PrimaryKey::Db(thing) => RecordId::from_str(thing.to_string().as_str()).unwrap(),
            PrimaryKey::LocalId(id) => RecordId::from((self.table_name(), id)),
            PrimaryKey::ZirozenId(id) => RecordId::from((self.table_name(), id.to_owned())),
        };

        let record: Option<Self> = DB.select(&record_id).await?;

        if let Some(record) = record {
            trace!(
                "Record received successfully from DB for id {}",
                record_id.to_string()
            );
            Ok(record)
        } else {
            Err(DatabaseError::NoneReturnFromDb(format!(
                "Record return None for id {:?}",
                record_id,
            )))
        }
    }

    async fn load_with_connection(self, db: Surreal<Db>) -> Result<Self, DatabaseError> {
        let record_id: RecordId = match self.get_primary_key() {
            PrimaryKey::Db(thing) => RecordId::from_str(thing.to_string().as_str()).unwrap(),
            PrimaryKey::LocalId(id) => RecordId::from((self.table_name(), id)),
            PrimaryKey::ZirozenId(id) => RecordId::from((self.table_name(), id.to_owned())),
        };

        let record: Option<Self> = db.select(&record_id).await?;

        if let Some(record) = record {
            trace!(
                "Record received successfully from DB for id {}",
                record_id.to_string()
            );
            Ok(record)
        } else {
            Err(DatabaseError::NoneReturnFromDb(format!(
                "Record return None for id {:?}",
                record_id,
            )))
        }
    }

    async fn persist_with_connection(&self, db: Surreal<Db>) -> Result<Self, DatabaseError> {
        let record_id: RecordId = match self.get_primary_key() {
            PrimaryKey::Db(thing) => RecordId::from_str(thing.to_string().as_str()).unwrap(),
            PrimaryKey::LocalId(id) => RecordId::from((self.table_name(), id)),
            PrimaryKey::ZirozenId(id) => RecordId::from((self.table_name(), id.to_owned())),
        };

        trace!("Saving record {:?} with id {:?}", self, record_id);

        let item = self.clone();

        let record: Option<Self> = db.upsert(record_id).content(item).await?;

        if let Some(record) = record {
            trace!("Record {:?} saved successfully to DB", record);
            Ok(record)
        } else {
            error!("Failed to save record {:?} response from db is none", self);
            Err(DatabaseError::NoneReturnFromDb(format!(
                "Saving Record return None for record {:?}",
                self,
            )))
        }
    }

    async fn persist(&self) -> Result<Self, DatabaseError> {
        let record_id: RecordId = match self.get_primary_key() {
            PrimaryKey::Db(thing) => RecordId::from_str(thing.to_string().as_str()).unwrap(),
            PrimaryKey::LocalId(id) => RecordId::from((self.table_name(), id)),
            PrimaryKey::ZirozenId(id) => RecordId::from((self.table_name(), id.to_owned())),
        };

        trace!("Saving record {:?} with id {:?}", self, record_id);

        let item = self.clone();

        let record: Option<Self> = DB.upsert(record_id).content(item).await?;

        if let Some(record) = record {
            trace!("Record {:?} saved successfully to DB", record);
            Ok(record)
        } else {
            error!("Failed to save record {:?} response from db is none", self);
            Err(DatabaseError::NoneReturnFromDb(format!(
                "Saving Record return None for record {:?}",
                self,
            )))
        }
    }

    async fn delete(&self) -> Result<Self, DatabaseError> {
        let record_id: RecordId = match self.get_primary_key() {
            PrimaryKey::Db(thing) => RecordId::from_str(thing.to_string().as_str()).unwrap(),
            PrimaryKey::LocalId(id) => RecordId::from((self.table_name(), id)),
            PrimaryKey::ZirozenId(id) => RecordId::from((self.table_name(), id.to_owned())),
        };

        trace!("Deleting record with id {:?} from database", record_id);

        let record: Option<Self> = DB.delete(record_id).await?;

        if let Some(record) = record {
            trace!("Record {:?} deleted successfully from DB", record);
            Ok(record)
        } else {
            error!(
                "Failed to delete record {:?} response from db is none",
                self
            );
            Err(DatabaseError::NoneReturnFromDb(format!(
                "Deleting Record return None for record {:?}",
                self,
            )))
        }
    }
}
