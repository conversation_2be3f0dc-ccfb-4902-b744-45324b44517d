use crate::{args::CmdArgs, prelude::EndpointopsError};
use database::{
    models::{AgentConfig, AgentMetadata, ServerConfig, Task, TaskType},
    PrimaryKey,
};
use logger::{debug, error, info, WithSubscriber};
use std::path::PathBuf;
use tokio::fs;
use utils::{bin_config::read_config, dir::get_current_dir};

pub async fn get_current_server_config(args: &CmdArgs) -> Result<ServerConfig, EndpointopsError> {
    match read_config::<ServerConfig, _>(PathBuf::from(&args.install_path).join("config.bin")) {
        Ok(config) => Ok(config),
        Err(error) => {
            error!(?error, "Failed to read config.bin file");
            Err(EndpointopsError::ConfigReadError(format!(
                "Failed to read config.bin file {:?}",
                error
            )))
        }
    }
}

pub async fn run_upgrade_task() -> Result<i32, EndpointopsError> {
    let task = Task {
        id: PrimaryKey::LocalId("Upgrade".to_owned()),
        task_type: Some(TaskType::Upgrade),
        ..Task::default()
    };

    match task_execution::build_executable_task(task, AgentMetadata::new(1, AgentConfig::default()))
    {
        Ok(mut task) => {
            debug!("Build Task {:?}", task);
            let task_logger = task.logger();
            let result = task
                .operate()
                .with_subscriber(task_logger.subscriber())
                .await;
            info!("Upgrade task finished with result {:?}", result);
            if let Err(error) = task.remove_task_resources().await {
                error!(?error, "Failed to remove upgrade task resources");
            }
            if let Err(error) = fs::remove_dir_all(get_current_dir().join("upgrade")).await {
                error!(?error, "Failed to remove upgrade directory");
            }
            drop(task);
            return Ok(result.exit_code);
        }
        Err(error) => {
            error!(?error, "Failed to build executable task for upgrade");
            return Ok(2);
        }
    };
}
