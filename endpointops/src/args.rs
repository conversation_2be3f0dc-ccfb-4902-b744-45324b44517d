use clap::<PERSON>rse<PERSON>;

#[derive(<PERSON><PERSON><PERSON>, Debug, <PERSON><PERSON>)]
#[command(about, long_about = None)]
pub struct CmdArgs {
    #[arg(long, default_value_t = false)]
    pub version: bool,

    #[arg(long, default_value_t = true)]
    pub install: bool,

    #[arg(long, default_value_t = false)]
    pub uninstall: bool,

    #[arg(long, default_value_t = false)]
    pub accept_eula: bool,

    #[cfg(windows)]
    #[arg(short, long, default_value_t = String::from("C:\\Program Files\\zirozen"))]
    pub install_path: String,

    #[cfg(not(windows))]
    #[arg(short, long, default_value_t = String::from("/opt/zirozen/agent"))]
    pub install_path: String,

    #[arg(short, long, default_value_t = false)]
    pub manager: bool,

    #[arg(short, long, default_value_t = false)]
    pub foreground: bool,

    #[arg(short, long, default_value_t = false)]
    pub agent: bool,

    #[arg(short, long, default_value_t = String::from("info"))]
    pub log: String,

    #[arg(long)]
    pub uuid: Option<String>,

    #[arg(long)]
    pub remote_url: Option<String>,

    #[arg(short, long)]
    pub enroll_id: Option<i64>,

    #[arg(long, default_value_t = false)]
    pub wol: bool,

    #[arg(long)]
    pub mac: Option<String>,
}
