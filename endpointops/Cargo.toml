[package]
name = "EndpointOps"
description = "An Entry point for the EndpointOps Agent"
default-run = "endpointops"
edition.workspace = true
version.workspace = true
authors.workspace = true

[[bin]]
name = "endpointops"
path = "src/main.rs"

[dependencies]
database = { path = "../database" }
logger = { path = "../logger" }
agents = { path = "../agents" }
api = { path = "../api" }
utils = { path = "../utils" }
agent_manager = { path = "../agent_manager" }
task_execution = { path = "../task_execution" }
data_collection = { path = "../data_collection" }
tokio = { version = "1.46.1", features = ["full", "tracing"] }
config = { version = "0.15.13", features = ["ini"] }
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
backtrace = "0.3.75"
figlet-rs = "0.1.5"
console = "0.16.0"
clap = { version = "4.5.41", features = ["derive"] }
rayon = "1.10.0"
textwrap = "0.16.2"
crossterm = "0.29.0"
self-replace = "=1.5.0"
cargo-util = "0.2.21"
wake-on-lan = "0.2.0"

[build-dependencies]
build-target = "0.7.0"

[target.'cfg(windows)'.build-dependencies]
embed-resource = "3.0.5"

[target.'cfg(windows)'.dependencies]
windows-service = "0.8.0"
winreg = "0.55.0"
