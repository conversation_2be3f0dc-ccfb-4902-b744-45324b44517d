use crate::{
    cpp_data_types::{
        MsgDataType, Operation, COMMON_MONINFO, FILE_NAME_SIZE, IOCTL_MSG, MESSAGE_DATA,
    },
    path_utils::{
        build_logical_to_physical_map, convert_dos_path_to_nt_path, dos_to_nt_registry_path,
        nt_to_dos_registry_path, physical_to_logical,
    },
    proc_owner::ProcOwner,
    thread_loop_handler::Thread<PERSON><PERSON><PERSON>and<PERSON>,
    win_driver_controller::WinDriverController,
    ProcessEvent, WinDriverEvent, WinDriverHandlerError,
};
use dashmap::DashMap;
use std::{os::raw::c_void, path::Path, process::Command, sync::Arc, thread};
use tokio::sync::mpsc::Sender;
use tracing::{debug, error, info, trace};
use windows::{
    core::{Error as WindowNativeError, PCWSTR},
    Win32::{
        Foundation::{CloseHandle, GetLast<PERSON>rror, HAND<PERSON>, WAIT_OBJECT_0, WAIT_TIMEOUT},
        Storage::{
            FileSystem::{
                CreateFileW, FILE_ATTRIBUTE_NORMAL, FILE_GENERIC_READ, FILE_GENERIC_WRITE,
                FILE_SHARE_READ, FILE_SHARE_WRITE, OPEN_EXISTING,
            },
            InstallableFileSystems::{
                FilterConnectCommunicationPort, FilterGetMessage, FILTER_MESSAGE_HEADER,
            },
        },
        System::{
            Services::{
                CloseServiceHandle, ControlService, OpenSCManagerW, OpenServiceW,
                QueryServiceStatus, StartServiceW, SC_MANAGER_ALL_ACCESS, SERVICE_CONTROL_STOP,
                SERVICE_QUERY_STATUS, SERVICE_START, SERVICE_STATUS, SERVICE_STOP,
                SERVICE_STOP_PENDING,
            },
            Threading::WaitForSingleObject,
            IO::DeviceIoControl,
        },
    },
};

const BUFFER_SIZE: usize = 1 << 16; // 64 kb

fn wide_string(s: &str) -> Vec<u16> {
    use std::os::windows::ffi::OsStrExt;
    std::ffi::OsStr::new(s)
        .encode_wide()
        .chain(std::iter::once(0))
        .collect()
}

pub struct WinDriverHandler<'a> {
    driver_name: &'a str,
    file_name: &'a str,
    port: &'a str,
    logical_drive_to_physical_map: DashMap<String, String>,
    controller: Arc<ThreadLoopHandler>,
    proc_data: DashMap<u32, ProcOwner>,
    channel: Sender<WinDriverEvent>,
}

impl WinDriverHandler<'static> {
    pub fn new(
        service_name: &'static str,
        file_name: &'static str,
        port: &'static str,
        channel: Sender<WinDriverEvent>,
    ) -> WinDriverHandler<'static> {
        Self {
            driver_name: service_name,
            file_name,
            port,
            logical_drive_to_physical_map: build_logical_to_physical_map(),
            controller: Arc::new(ThreadLoopHandler::new()),
            proc_data: WinDriverHandler::build_proc_data(),
            channel,
        }
    }

    fn build_proc_data() -> DashMap<u32, ProcOwner> {
        let map = DashMap::new();
        let data = ProcOwner::get_all_running();
        for process in data {
            map.insert(process.get_pid(), process);
        }
        map
    }

    fn send_message_to_driver(
        &self,
        mut message: MESSAGE_DATA,
    ) -> Result<(), WinDriverHandlerError> {
        let handle = unsafe {
            match CreateFileW(
                PCWSTR::from_raw(wide_string(self.file_name).as_ptr()),
                (FILE_GENERIC_READ | FILE_GENERIC_WRITE).0,
                FILE_SHARE_READ | FILE_SHARE_WRITE,
                None,
                OPEN_EXISTING,
                FILE_ATTRIBUTE_NORMAL,
                None,
            ) {
                Ok(handle) => handle,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to open connection to driver to send message"
                    );
                    return Err(WinDriverHandlerError::FailedToConnectDriverPort(error));
                }
            }
        };

        let mut bytes_returned = 0u32;

        unsafe {
            if let Err(error) = DeviceIoControl(
                handle,
                IOCTL_MSG,
                Some(&mut message as *mut _ as *mut c_void),
                size_of::<MESSAGE_DATA>() as u32,
                None,
                0,
                Some(&mut bytes_returned),
                None,
            ) {
                debug!("Bytes returned {bytes_returned}");
                if let Err(error) = CloseHandle(handle) {
                    error!(
                        ?error,
                        "Failed to close device handle after sending message using deviceiocontrol"
                    );
                };
                return Err(WinDriverHandlerError::FailedToSendMessage(error));
            }
        };

        if let Err(error) = unsafe { CloseHandle(handle) } {
            error!(
                ?error,
                "Failed to close device handle after sending message using deviceiocontrol"
            );
        };
        Ok(())
    }

    fn prepare_and_send_message_to_driver<P: AsRef<Path>>(
        &self,
        path: P,
        r#type: MsgDataType,
        operation: Operation,
    ) -> Result<(), WinDriverHandlerError> {
        let nt_path_utf16 = if operation != Operation::Clear {
            let path_str = match path.as_ref().to_str() {
                Some(value) => value,
                None => {
                    error!("Failed to convert path to string");
                    return Err(WinDriverHandlerError::InvalidPath(
                        path.as_ref().to_path_buf(),
                    ));
                }
            };

            let nt_path = match r#type {
                MsgDataType::Directory | MsgDataType::File => convert_dos_path_to_nt_path(path_str),
                MsgDataType::Registry => dos_to_nt_registry_path(path_str),
                MsgDataType::Process => path_str.to_owned(),
            };

            if nt_path.is_empty() {
                return Ok(());
            }

            debug!("Using NT Path {}", nt_path);
            wide_string(&nt_path)
        } else {
            wide_string("")
        };

        let mut msg = MESSAGE_DATA {
            Type: r#type,
            Op: operation,
            Size: (nt_path_utf16.len() - 1) as i32 * 2, // without null terminator
            Data: [0u16; FILE_NAME_SIZE],
        };

        msg.Data[..nt_path_utf16.len().min(FILE_NAME_SIZE)]
            .copy_from_slice(&nt_path_utf16[..nt_path_utf16.len().min(FILE_NAME_SIZE)]);

        self.send_message_to_driver(msg)
    }

    pub fn watch_folder<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::Directory, Operation::Include)
    }

    pub fn exclude_folder<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::Directory, Operation::Exclude)
    }

    pub fn block_directory_access<P: AsRef<Path>>(
        &self,
        path: P,
    ) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::Directory, Operation::Block)
    }

    pub fn exclude_directory_block<P: AsRef<Path>>(
        &self,
        path: P,
    ) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(
            path,
            MsgDataType::Directory,
            Operation::BlockExclude,
        )
    }

    pub fn block_file_access<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(
            path,
            MsgDataType::File,
            Operation::FullFileNameBlock,
        )
    }

    pub fn exclude_file_block<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(
            path,
            MsgDataType::File,
            Operation::FullFileNameExclude,
        )
    }

    pub fn block_process<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::Process, Operation::Block)
    }

    pub fn exclude_process_block<P: AsRef<Path>>(
        &self,
        path: P,
    ) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::File, Operation::BlockExclude)
    }

    pub fn unwatch_folder<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        let operation = if path.as_ref().to_string_lossy().is_empty() {
            Operation::Clear
        } else {
            Operation::Ignore
        };
        self.prepare_and_send_message_to_driver(path, MsgDataType::Directory, operation)
    }

    pub fn watch_registry<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::Registry, Operation::Include)
    }

    pub fn exclude_registry<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        self.prepare_and_send_message_to_driver(path, MsgDataType::Registry, Operation::Exclude)
    }

    pub fn unwatch_registry<P: AsRef<Path>>(&self, path: P) -> Result<(), WinDriverHandlerError> {
        let operation = if path.as_ref().to_string_lossy().is_empty() {
            Operation::Clear
        } else {
            Operation::Ignore
        };
        self.prepare_and_send_message_to_driver(path, MsgDataType::Registry, operation)
    }

    fn process_and_notify_of_event(
        mut event: WinDriverEvent,
        channel: &Sender<WinDriverEvent>,
        drive_map: &DashMap<String, String>,
        proc_data: &DashMap<u32, ProcOwner>,
    ) -> Result<(), WinDriverHandlerError> {
        if event.is_process_create_event() && event.get_process_data().is_some() {
            let mut process_detail = event.get_process_data().unwrap().to_owned();
            process_detail.name = Some(
                process_detail
                    .name
                    .map_or("".to_owned(), |v| physical_to_logical(&v, drive_map)),
            );
            if proc_data.contains_key(&process_detail.pid) == false {
                if let Ok(proc_owner) =
                    <ProcessEvent as TryInto<ProcOwner>>::try_into(process_detail)
                {
                    let pid = proc_owner.get_pid();
                    // put new proc data inside map
                    proc_data.insert(proc_owner.get_pid(), proc_owner);
                    trace!("Inserted proc owner with id {}", pid);
                }
            }
        } else if event.is_process_exit_event() && event.get_process_data().is_some() {
            let process_detail = event.get_process_data().unwrap();
            proc_data.remove(&process_detail.pid);
        } else if event.is_file_event() && event.get_file_data().is_some() {
            // convert physical path to logical
            let mut file_detail = event.get_file_data().unwrap().to_owned();
            file_detail.file_name = physical_to_logical(&file_detail.file_name, drive_map)
                .replace(":Zone.Identifier", "");
            if file_detail.renamed_file_name.is_some() {
                file_detail.renamed_file_name = Some(physical_to_logical(
                    file_detail.renamed_file_name.as_ref().unwrap(),
                    drive_map,
                ));
            }
            if let Some(proc_owner) = proc_data.get(&file_detail.pid) {
                file_detail.proc_info = proc_owner.clone();
            }
            event.set_file_data(file_detail);
        } else if event.is_registry_event() && event.get_registry_data().is_some() {
            // change registry path to given path
            let mut registry_detail = event.get_registry_data().unwrap().to_owned();
            registry_detail.key_name = nt_to_dos_registry_path(&registry_detail.key_name);
            if registry_detail.updated_key_name.is_some() {
                registry_detail.updated_key_name = Some(nt_to_dos_registry_path(
                    &registry_detail.updated_key_name.as_ref().unwrap(),
                ));
            }
            if let Some(proc_owner) = proc_data.get(&registry_detail.pid) {
                registry_detail.proc_info = proc_owner.clone();
            }
            event.set_registry_data(registry_detail);
        }

        match channel.blocking_send(event) {
            Ok(()) => {
                trace!("Event sent successfully");
                Ok(())
            }
            Err(error) => {
                error!(?error, "Failed to send event to main channel");
                Ok(())
            }
        }
    }

    fn perform_fltmc_operation(
        driver_name: &str,
        op_type: &str,
    ) -> Result<(), WinDriverHandlerError> {
        let cmd = Command::new("fltmc").arg(op_type).arg(driver_name).spawn();

        let mut child = match cmd {
            Ok(child) => child,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to spawn fltmc command to {} driver", op_type
                );
                return Err(WinDriverHandlerError::IOError(error));
            }
        };

        match child.wait() {
            Ok(status) => {
                if status.success() {
                    info!("Driver {} successfully", op_type);
                } else {
                    error!(
                        "Driver {} failed with status {}",
                        op_type,
                        status.code().unwrap_or_else(|| 99)
                    );
                }
            }
            Err(error) => {
                error!(?error, "Failed to wait for fltmc {} command", op_type);
                return Err(WinDriverHandlerError::IOError(error));
            }
        }

        Ok(())
    }

    pub fn unload(driver_name: &str) -> Result<(), WinDriverHandlerError> {
        WinDriverHandler::perform_fltmc_operation(driver_name, "unload")
    }

    pub fn load(driver_name: &str) -> Result<(), WinDriverHandlerError> {
        WinDriverHandler::perform_fltmc_operation(driver_name, "load")
    }

    pub fn restart_driver_service(&self) -> windows::core::Result<()> {
        let scm_handle = match unsafe { OpenSCManagerW(None, None, SC_MANAGER_ALL_ACCESS) } {
            Ok(handle) => handle,
            Err(error) => {
                error!(?error, "Failed to open service manager");
                return Err(error);
            }
        };

        if scm_handle.is_invalid() {
            let error = unsafe { GetLastError() };
            error!("Failed to open SCM: {:?}", error);
            return Err(WindowNativeError::new(
                error.to_hresult(),
                "Failed to open SCM",
            ));
        }

        let service_handle = match unsafe {
            OpenServiceW(
                scm_handle,
                PCWSTR(wide_string(self.driver_name).as_ptr()),
                SERVICE_STOP | SERVICE_START | SERVICE_QUERY_STATUS,
            )
        } {
            Ok(handle) => handle,
            Err(error) => {
                error!(?error, "Failed to open service {}", self.driver_name);
                return Err(error);
            }
        };

        if service_handle.is_invalid() {
            let error = unsafe { GetLastError() };
            error!("Failed to open service: {:?}", error);
            return Err(WindowNativeError::new(
                error.to_hresult(),
                "Failed to open driver service",
            ));
        }

        // Try stopping the service
        let mut status = SERVICE_STATUS::default();
        unsafe { ControlService(service_handle, SERVICE_CONTROL_STOP, &mut status) }.ok();

        // Optional: Wait until it stops
        let mut service_status = SERVICE_STATUS::default();
        loop {
            match unsafe { QueryServiceStatus(service_handle, &mut service_status) } {
                Err(error) => {
                    error!(?error, "Failed to get current status of driver service");
                    return Err(error);
                }
                _ => {}
            };
            if service_status.dwCurrentState != SERVICE_STOP_PENDING {
                break;
            }
            std::thread::sleep(std::time::Duration::from_millis(500));
        }

        // Start the service
        match unsafe { StartServiceW(service_handle, None) } {
            Ok(_) => {
                info!("Driver {} is started successfully.", self.driver_name);
            }
            Err(error) => {
                error!(?error, "Failed to start driver Service");
                return Err(error);
            }
        };

        unsafe {
            if let Err(error) = CloseServiceHandle(service_handle) {
                error!(?error, "Failed to close service handle");
            };
            if let Err(error) = CloseServiceHandle(scm_handle) {
                error!(?error, "Failed to close SCM handler");
            }
        }

        Ok(())
    }

    pub fn start(self) -> Result<WinDriverController, WinDriverHandlerError> {
        self.controller.start();

        let return_controller = Arc::clone(&self.controller);
        let controller = Arc::clone(&self.controller);

        let port = self.port.to_owned();

        let channel = self.channel;
        let proc_data = self.proc_data.clone();
        let drive_map = self.logical_drive_to_physical_map.clone();

        let join_handle = thread::spawn(move || {
            let port_name = wide_string(&port);
            let h_port = unsafe {
                match FilterConnectCommunicationPort(
                    PCWSTR::from_raw(port_name.as_ptr()),
                    0,
                    None,
                    0,
                    None,
                ) {
                    Ok(handle) => handle,
                    Err(error) => {
                        error!(?error, "Failed to connect to communcation port {}", port);
                        return Err(WinDriverHandlerError::WindowNativeError(error));
                    }
                }
            };
            // Allocate buffer for message
            let mut buffer = vec![0u8; BUFFER_SIZE];
            let buffer_ptr = buffer.as_mut_ptr() as *mut FILTER_MESSAGE_HEADER;
            loop {
                if controller.should_continue() == false || channel.is_closed() {
                    debug!("Should continue in driver {}", controller.should_continue());
                    break;
                }

                match WinDriverHandler::try_recv(h_port, buffer_ptr) {
                    Ok(event) => {
                        if channel.is_closed() {
                            break;
                        }
                        if let Some(mut event) = event {
                            trace!("Received Event {:?}", event);
                            if event
                                .get_file_data()
                                .is_some_and(|f| f.is_temp_event(event.get_event_type()))
                            {
                                trace!("Ignoring unnecessary event");
                                continue;
                            }

                            // here handle rename event of tmp extension
                            if event.is_file_event() {
                                event = event.process_rename_file_operation();
                            }

                            if let Err(error) = WinDriverHandler::process_and_notify_of_event(
                                event, &channel, &drive_map, &proc_data,
                            ) {
                                error!(?error, "Failed to send message to receiver");
                            }
                        }
                    }
                    Err(error) => {
                        error!(?error, "Failed to receive message from driver");
                    }
                }
            }
            if let Err(error) = unsafe { CloseHandle(h_port) } {
                error!(?error, "Failed to close filtergetmessage handle");
            }
            Ok(())
        });
        Ok(WinDriverController::new(return_controller, join_handle))
    }

    fn try_recv(
        h_port: HANDLE,
        buffer_ptr: *mut FILTER_MESSAGE_HEADER,
    ) -> Result<Option<WinDriverEvent>, WinDriverHandlerError> {
        let wait_result = unsafe { WaitForSingleObject(h_port, 500) };

        match wait_result {
            WAIT_OBJECT_0 => {
                if let Err(error) =
                    unsafe { FilterGetMessage(h_port, buffer_ptr, BUFFER_SIZE as u32, None) }
                {
                    error!(?error, "Failed to get message from driver");
                    return Err(WinDriverHandlerError::WindowNativeError(error));
                }
                let event_data_ptr = unsafe {
                    (buffer_ptr as *const u8).add(size_of::<FILTER_MESSAGE_HEADER>())
                        as *const COMMON_MONINFO
                };

                let event_info = unsafe { &*event_data_ptr };

                match WinDriverEvent::try_from(event_info) {
                    Ok(event) => Ok(Some(event)),
                    Err(error) => {
                        error!(?error, "Failed to convert driver message to WinDriverEvent");
                        Err(error)
                    }
                }
            }
            WAIT_TIMEOUT => {
                debug!("Timeout happened before getting message");
                Ok(None)
            }
            _ => Err(WinDriverHandlerError::WindowNativeError(
                WindowNativeError::from_win32(),
            )),
        }
    }
}
