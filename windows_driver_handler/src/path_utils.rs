use dashmap::DashMap;
use std::{
    collections::HashMap,
    ffi::{CStr, CString, OsString},
    os::windows::ffi::OsStringExt,
    path::Path,
};
use tracing::error;
use windows::{
    core::PCSTR,
    Win32::{
        Storage::FileSystem::{GetLogicalDriveStringsA, QueryDosDeviceA},
        System::SystemInformation::GetWindowsDirectoryW,
    },
};

pub fn build_logical_to_physical_map() -> DashMap<String, String> {
    let logical_to_physical = DashMap::new();

    let mut drive_strings = [0u8; 200];
    let size = unsafe { GetLogicalDriveStringsA(Some(&mut drive_strings)) as usize };

    let mut i = 0;
    while i < size {
        // Get a single null-terminated drive string (like "C:\\")
        let drive_cstr = unsafe { CStr::from_ptr(drive_strings[i..].as_ptr() as *const i8) };
        let drive = drive_cstr.to_string_lossy().to_string();

        // QueryDosDevice expects something like "C:"
        let device_query = drive.trim_end_matches('\\');
        let c_device_query = CString::new(device_query).unwrap();

        let mut vname = [0u8; 261];
        unsafe {
            QueryDosDeviceA(
                PCSTR::from_raw(c_device_query.as_ptr() as *const u8),
                Some(&mut vname),
            )
        };

        let device_path = unsafe { CStr::from_ptr(vname.as_ptr() as *const i8) }
            .to_string_lossy()
            .to_string();

        let key = format!("{}\\", device_path);
        logical_to_physical.insert(key, drive.clone());

        // Move to next null-terminated drive string
        i += drive_cstr.to_bytes_with_nul().len();
    }
    logical_to_physical
}

fn get_windows_directory() -> String {
    let mut buffer = [0u16; 260];
    let len = unsafe { GetWindowsDirectoryW(Some(&mut buffer)) } as usize;
    if len == 0 {
        return "C:\\Windows".to_owned();
    }
    OsString::from_wide(&buffer[..len])
        .to_string_lossy()
        .into_owned()
}

pub fn physical_to_logical(nt_path: &str, drive_map: &DashMap<String, String>) -> String {
    if let Some(stripped) = nt_path.strip_prefix(r"\??\") {
        return stripped.to_string(); // \??\C:\Windows → C:\Windows
    }

    if let Some(stripped) = nt_path.strip_prefix(r"\SystemRoot\") {
        let win_dir = get_windows_directory();
        return format!(r"{}\{}", win_dir, stripped);
    }

    if nt_path.starts_with(r"\Device\") {
        let path = Path::new(nt_path);
        for item in drive_map.iter() {
            if path.starts_with(item.key()) {
                return path
                    .to_string_lossy()
                    .replace(item.key(), item.value())
                    .to_owned();
            }
        }
    }

    return nt_path.to_owned();
}

pub fn convert_dos_path_to_nt_path(path: &str) -> String {
    let drive_prefix = &path[..2];
    let c_device_query = CString::new(drive_prefix).unwrap();

    let mut vname = [0u8; 261];
    unsafe {
        QueryDosDeviceA(
            PCSTR::from_raw(c_device_query.as_ptr() as *const u8),
            Some(&mut vname),
        )
    };

    let device_path = unsafe { CStr::from_ptr(vname.as_ptr() as *const i8) }
        .to_string_lossy()
        .to_string();
    if device_path.is_empty() {
        error!("Unable to resolve path {}", path);
        return "".to_owned();
    }
    // Step 3: Replace "C:\" with device path
    let relative_path = &path[2..]; // skip "C:"
    Path::new(format!("{}{}", device_path, relative_path).as_str())
        .to_string_lossy()
        .to_string()
}

// pub fn get_logged_in_user() -> Option<String> {
//     // Step 1: Get active session ID
//     let session_id = unsafe { WTSGetActiveConsoleSessionId() };
//     if session_id == u32::MAX {
//         let error = Error::from_win32();
//         error!(?error, "Failed to get current sessionId");
//         return None;
//     }

//     // Step 2: Get user token from session
//     let mut user_token = HANDLE::default();
//     if let Err(error) = unsafe { WTSQueryUserToken(session_id, &mut user_token) } {
//         error!(?error, "Failed to get current active session token");
//         return None;
//     }

//     // Step 3: Get SID from token
//     let mut token_user: Vec<u8> = vec![0; 512];
//     let mut return_length = 0;
//     if let Err(error) = unsafe {
//         GetTokenInformation(
//             user_token,
//             TokenUser,
//             Some(token_user.as_mut_ptr() as *mut _),
//             token_user.len() as u32,
//             &mut return_length,
//         )
//     } {
//         unsafe {
//             match CloseHandle(user_token) {
//                 Err(error) => {
//                     error!(?error, "Failed to close handle for user token");
//                 }
//                 _ => {}
//             }
//         };
//         error!(?error, "Failed to get token information for current User");
//         return None;
//     }

//     let token_user_ptr = token_user.as_ptr() as *const TOKEN_USER;
//     let sid_ptr = unsafe { (*token_user_ptr).User.Sid };

//     // Step 4: Convert SID to string
//     let mut sid_string_ptr: PWSTR = PWSTR::null();
//     if let Err(error) = unsafe { ConvertSidToStringSidW(sid_ptr, &mut sid_string_ptr) } {
//         unsafe {
//             match CloseHandle(user_token) {
//                 Err(error) => {
//                     error!(?error, "Failed to close handle for user token");
//                 }
//                 _ => {}
//             }
//         };
//         error!(?error, "Failed to convert sid to string");
//         return None;
//     }

//     let sid_string = unsafe { sid_string_ptr.to_string() }.unwrap_or_default();
//     unsafe { LocalFree(Some(HLOCAL(sid_string_ptr.as_ptr() as *mut _))) };
//     unsafe {
//         match CloseHandle(user_token) {
//             Err(error) => {
//                 error!(?error, "Failed to close handle for user token");
//             }
//             _ => {}
//         }
//     };

//     Some(sid_string)
// }

pub fn expand_registry_path(default_path: &str) -> String {
    let path = default_path.trim_start_matches('\\');
    let mappings = HashMap::from([
        ("HKLM", r"HKEY_LOCAL_MACHINE"),
        ("HKCR", r"HKEY_CLASSES_ROOT"),
        ("HKU", r"HKEY_USERS"),
        ("HKCC", r"HKEY_CURRENT_CONFIG"),
    ]);

    // let full_sid_path;

    // if let Some(sid) = get_logged_in_user() {
    //     full_sid_path = format!(r"\REGISTRY\USER\{}", sid);
    //     mappings.insert("HKEY_CURRENT_USER", &full_sid_path);
    // }

    for (dos_root, nt_root) in mappings {
        if let Some(stripped) = path.strip_prefix(dos_root) {
            return format!("{}{}", nt_root, stripped);
        }
    }

    path.to_owned()
}

pub fn dos_to_nt_registry_path(dos_path: &str) -> String {
    let path = dos_path.trim_start_matches('\\');
    let mappings = HashMap::from([
        ("HKEY_LOCAL_MACHINE", r"\REGISTRY\MACHINE"),
        ("HKLM", r"\REGISTRY\MACHINE"),
        ("HKEY_CLASSES_ROOT", r"\REGISTRY\MACHINE\Software\Classes"),
        ("HKCR", r"\REGISTRY\MACHINE\Software\Classes"),
        ("HKEY_USERS", r"\REGISTRY\USER"),
        ("HKU", r"\REGISTRY\USER"),
        (
            "HKEY_CURRENT_CONFIG",
            r"\REGISTRY\MACHINE\System\CurrentControlSet\Hardware Profiles\Current",
        ),
        (
            "HKCC",
            r"\REGISTRY\MACHINE\System\CurrentControlSet\Hardware Profiles\Current",
        ),
    ]);

    // let full_sid_path;

    // if let Some(sid) = get_logged_in_user() {
    //     full_sid_path = format!(r"\REGISTRY\USER\{}", sid);
    //     mappings.insert("HKEY_CURRENT_USER", &full_sid_path);
    // }

    for (dos_root, nt_root) in mappings {
        if let Some(stripped) = path.strip_prefix(dos_root) {
            return format!("{}{}", nt_root, stripped);
        }
    }

    dos_path.to_owned()
}

/// Convert NT-style registry path to DOS-style.
/// E.g. \Registry\Machine\Software => HKEY_LOCAL_MACHINE\Software
pub fn nt_to_dos_registry_path(nt_path: &str) -> String {
    let path = nt_path.trim_start_matches('\\');
    let mappings = HashMap::from([
        (r"REGISTRY\MACHINE\Software\Classes", "HKEY_CLASSES_ROOT"),
        (
            r"REGISTRY\MACHINE\System\CurrentControlSet\Hardware Profiles\Current",
            "HKEY_CURRENT_CONFIG",
        ),
        (r"REGISTRY\MACHINE", "HKEY_LOCAL_MACHINE"),
        (r"REGISTRY\USER", "HKEY_USERS"),
    ]);

    // let sid_full_path;

    // if let Some(sid) = get_logged_in_user() {
    //     sid_full_path = format!(r"REGISTRY\USER\{}", sid);
    //     mappings.insert(&sid_full_path, "HKEY_CURRENT_USER");
    // }

    for (nt_root, dos_root) in mappings {
        if let Some(stripped) = path.strip_prefix(nt_root) {
            return format!(r"{}{}", dos_root, stripped);
        }
    }

    nt_path.to_owned()
}
