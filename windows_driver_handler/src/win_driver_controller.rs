use crate::{thread_loop_handler::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WinDriverHandlerError};
use anyhow::anyhow;
use std::{sync::Arc, thread::<PERSON><PERSON><PERSON><PERSON><PERSON>};
use tracing::error;

pub struct WinDriverController {
    loop_controller: Arc<ThreadLoopHandler>,
    join_handle: <PERSON><PERSON><PERSON><PERSON><PERSON><Result<(), WinDriverHandlerError>>,
}

impl WinDriverController {
    pub(crate) fn new(
        loop_controller: Arc<ThreadLoopHandler>,
        join_handle: <PERSON><PERSON><PERSON><PERSON><PERSON><Result<(), WinDriverHandlerError>>,
    ) -> Self {
        WinDriverController {
            loop_controller,
            join_handle,
        }
    }

    pub fn stop(&self) {
        self.loop_controller.stop();
    }

    pub fn wait_for_exit(self) -> Result<(), WinDriverHandlerError> {
        match self.join_handle.join() {
            Ok(result) => result,
            Err(error) => {
                error!(?error, "Failed to join windows driver thread");
                Err(WinDriverHandlerError::<PERSON><PERSON>rror(anyhow!("{:?}", error)))
            }
        }
    }
}
