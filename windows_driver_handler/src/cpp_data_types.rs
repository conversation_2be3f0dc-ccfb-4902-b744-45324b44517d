#![allow(non_camel_case_types, non_snake_case, dead_code)]
use std::os::raw::{c_int, c_ulong, c_ushort};
pub const PROCESS_NAME_SIZE: usize = 256;
pub const COMMAND_LINE_SIZE: usize = 512;
pub const REGKEY_NAME_SIZE: usize = 1024;
pub const REGVALUE_SIZE: usize = 64;
pub const REG_DATA_SIZE: usize = 128;
pub const FILE_NAME_SIZE: usize = 1024;

// Driver tags (optional in Rust, but included for parity)
pub const DRIVER_TAG: u32 = 0x64767274; // 'tvrd'
pub const REGISTRY_TAG: u32 = 0x7265676A; // 'jger'
pub const FILE_TAG: u32 = 0x74656C66; // 'telf'
pub const SYSTEM_THREAD_TAG: u32 = 0x74736574; // 'tset'

#[repr(i16)]
#[derive(Debug, Co<PERSON>, Clone)]
pub enum EventType {
    None = 0,
    ProcessCreate,
    ProcessExit,
    FileCreate,
    FileDelete,
    FileRead,
    FileWrite,
    FileRename,
    DirRename,
    DirDelete,
    DirCreate,
    RegKeyCreate,
    RegKeyRename,
    RegValueCreate,
    RegistrySetValue,
    RegKeyDelete,
    RegValueDelete,
    RegiSaveKey,
    FileAccessBlock,
    ProcessBlock,
}

#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MsgDataType {
    Directory,
    Registry,
    File,
    Process,
}

#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Operation {
    Include,
    Exclude,
    Ignore,
    Clear,
    Block,
    BlockExclude,
    FullFileNameBlock,
    FullFileNameExclude,
}

// Structs
#[repr(C)]
#[derive(Debug, Clone, Copy)]
pub struct MESSAGE_DATA {
    pub Type: MsgDataType,
    pub Op: Operation,
    pub Size: c_int,
    pub Data: [u16; FILE_NAME_SIZE], // WCHAR = u16
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct EventHeader {
    pub eventType: EventType,
    pub size: c_ushort,
    pub Time: i64,
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ProcessCreateInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub ParentProcessId: c_ulong,
    pub SessionId: i32,
    pub ProcessName: [u16; PROCESS_NAME_SIZE],
    pub CommandLine: [u16; COMMAND_LINE_SIZE],
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ProcessExitInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub ParentProcessId: c_ulong,
    pub SessionId: i32,
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ProcBlockInfo {
    pub header: EventHeader,
    pub ProcessName: [u16; FILE_NAME_SIZE],
    pub ProcessPath: [u16; COMMAND_LINE_SIZE],
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct RegistrySetValueInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub KeyName: [u16; REGKEY_NAME_SIZE],
    pub NewKeyName: [u16; REGKEY_NAME_SIZE],
    pub ValueName: [u16; REGVALUE_SIZE],
    pub NewValueName: [u16; REGVALUE_SIZE],
    pub DataType: c_ulong,
    pub Data: [u16; REG_DATA_SIZE],
    pub DataSize: c_ulong,
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct FileCreateInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub FileObject: c_ulong,
    pub CreateOption: i32,
    pub FileName: [u16; FILE_NAME_SIZE],
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct FileDeleteInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub FileObject: c_ulong,
    pub FileName: [u16; FILE_NAME_SIZE],
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct FileReadInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub FileName: [u16; FILE_NAME_SIZE],
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct FileWriteInfo {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub FileName: [u16; FILE_NAME_SIZE],
}

#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct FileRenameINFO {
    pub header: EventHeader,
    pub ProcessId: c_ulong,
    pub SourceFileName: [u16; FILE_NAME_SIZE],
    pub TargetFileName: [u16; FILE_NAME_SIZE],
}

#[repr(C)]
#[derive(Copy, Clone)]
pub union EventInfoUnion {
    pub procCreateInfo: ProcessCreateInfo,
    pub procExitInfo: ProcessExitInfo,
    pub fileCreateInfo: FileCreateInfo,
    pub fileDeleteInfo: FileDeleteInfo,
    pub fileReadInfo: FileReadInfo,
    pub fileWriteInfo: FileWriteInfo,
    pub fileRenameInfo: FileRenameINFO,
    pub regSetValueInfo: RegistrySetValueInfo,
    pub procBlockInfo: ProcBlockInfo,
}

#[repr(C)]
#[derive(Copy, Clone)]
pub struct COMMON_MONINFO {
    pub eventType: EventType,
    pub eventInfo: EventInfoUnion,
}

// IOCTLs
pub const fn ctl_code(device_type: u32, function: u32, method: u32, access: u32) -> u32 {
    (device_type << 16) | (access << 14) | (function << 2) | method
}

pub const FILE_ANY_ACCESS: u32 = 0;
pub const METHOD_BUFFERED: u32 = 0;
pub const DEVICE_TYPE_ZIROZEN: u32 = 0x8000;

pub const IOCTL_MSG: u32 = ctl_code(0x8000, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS);
