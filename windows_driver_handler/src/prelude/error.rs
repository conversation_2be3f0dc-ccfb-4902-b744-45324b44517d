use crate::{cpp_data_types::EventType, ProcessEvent};
use anyhow::Error as AnyhowError;
use std::io::Error as IOError;
use std::path::PathBuf;
use thiserror::Error;
use windows::core::Error as WinError;

#[derive(Error, Debug)]
pub enum WinDriverHandlerError {
    #[error("Conversion Error: Failed to convert event to WinDriverEvent {0:?}")]
    FailedToConvertEvent(EventType),

    #[error("IO Error: Failed to perform I/O {0:?}")]
    IOError(IOError),

    #[error("Process Owner Error: Failed to retrive process owner data from {0:?}")]
    FailedToRetriveProcessOwner(ProcessEvent),

    #[error("Path Error: Failed to convert path to &str {0:?}")]
    InvalidPath(PathBuf),

    #[error("Connection Error: Failed to connect to driver socket to send message {0:?}")]
    FailedToConnectDriverPort(WinError),

    #[error(
        "Message Send Error: Failed to send message to driver socket using DeviceIoControl {0:?}"
    )]
    FailedToSendMessage(WinError),

    #[error("Windows Error: Failed to complete native windows Api {0:?}")]
    WindowNativeError(#[from] WinError),

    #[error("Thread Error: Windows Driver Thread error {0:?}")]
    UnknownError(#[from] AnyhowError),
}
