[package]
name = "windows_driver_handler"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
tokio = { version = "1.46.1" }
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
tracing = { version = "0.1.41", features = ["log", "std"] }
windows = { version = "0.61.3", features = [
  "Win32_Foundation",
  "Win32_Security",
  "Win32_Security_Authorization",
  "Win32_System_Threading",
  "Win32_System_RemoteDesktop",
  "Win32_System_SystemInformation",
  "Win32_Storage_FileSystem",
  "Win32_Storage_InstallableFileSystems",
  "Win32_System_IO",
  "Win32_System_Services",
] }
dashmap = "6.1.0"
