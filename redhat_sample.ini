[lvms-4.14-for-rhel-9-x86_64-debug-rpms]
name = Logical Volume Manager Storage 4.14 for RHEL 9 x86_64 (Debug RPMs)
baseurl = https://cdn.redhat.com/content/dist/layered/rhel9/x86_64/lvms/4.14/debug
enabled = 0
gpgcheck = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
sslverify = 1
sslcacert = /etc/rhsm/ca/redhat-uep.pem
sslclientkey = /etc/pki/entitlement/8794182316001181182-key.pem
sslclientcert = /etc/pki/entitlement/8794182316001181182.pem
metadata_expire = 86400
enabled_metadata = 0

[cnv-4.18-for-rhel-9-x86_64-rpms]
name = Red Hat Container Native Virtualization 4.18 for RHEL 9 x86_64 (RPMs)
baseurl = https://cdn.redhat.com/content/dist/layered/rhel9/x86_64/cnv/4.18/os
enabled = 0
gpgcheck = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
sslverify = 1
sslcacert = /etc/rhsm/ca/redhat-uep.pem
sslclientkey = /etc/pki/entitlement/8794182316001181182-key.pem
sslclientcert = /etc/pki/entitlement/8794182316001181182.pem
metadata_expire = 86400
enabled_metadata = 0

[rhocp-ironic-4.15-for-rhel-9-x86_64-rpms]
name = Ironic content for Red Hat OpenShift Container Platform 4.15 for RHEL 9 x86_64 (RPMs)
baseurl = https://cdn.redhat.com/content/dist/layered/rhel9/x86_64/rhocp-ironic/4.15/os
enabled = 0
gpgcheck = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
sslverify = 1
sslcacert = /etc/rhsm/ca/redhat-uep.pem
sslclientkey = /etc/pki/entitlement/8794182316001181182-key.pem
sslclientcert = /etc/pki/entitlement/8794182316001181182.pem
metadata_expire = 86400
enabled_metadata = 0

[gitops-1.13-for-rhel-9-x86_64-rpms]
name = Red Hat OpenShift GitOps 1.13 for RHEL 9 x86_64 (RPMs)
baseurl = https://cdn.redhat.com/content/dist/layered/rhel9/x86_64/gitops/1.13/os
enabled = 0
gpgcheck = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
sslverify = 1
sslcacert = /etc/rhsm/ca/redhat-uep.pem
sslclientkey = /etc/pki/entitlement/8794182316001181182-key.pem
sslclientcert = /etc/pki/entitlement/8794182316001181182.pem
metadata_expire = 86400
enabled_metadata = 0

[rhel-9-for-x86_64-appstream-eus-debug-rpms]
name = Red Hat Enterprise Linux 9 for x86_64 - AppStream - Extended Update Support (Debug RPMs)
baseurl = https://cdn.redhat.com/content/eus/rhel9/$releasever/x86_64/appstream/debug
enabled = 0
gpgcheck = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
sslverify = 1
sslcacert = /etc/rhsm/ca/redhat-uep.pem
sslclientkey = /etc/pki/entitlement/8794182316001181182-key.pem
sslclientcert = /etc/pki/entitlement/8794182316001181182.pem
metadata_expire = 86400
enabled_metadata = 0

[rhel-9-for-x86_64-appstream-e4s-rpms]
name = Red Hat Enterprise Linux 9 for x86_64 - AppStream - Update Services for SAP Solutions (RPMs)
baseurl = https://cdn.redhat.com/content/e4s/rhel9/$releasever/x86_64/appstream/os
enabled = 0
gpgcheck = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
sslverify = 1
sslcacert = /etc/rhsm/ca/redhat-uep.pem
sslclientkey = /etc/pki/entitlement/8794182316001181182-key.pem
sslclientcert = /etc/pki/entitlement/8794182316001181182.pem
metadata_expire = 86400
enabled_metadata = 0
