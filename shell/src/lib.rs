#[macro_use]
extern crate cfg_if;

mod prelude;
use logger::{debug, error};
pub use prelude::*;
use std::{path::Path, process::Stdio, str::from_utf8};
use tokio::{fs::OpenOptions, io::AsyncWriteExt, process::Command};
use utils::dir::get_current_dir;

#[derive(Debug, Default)]
pub struct ShellOutput {
    pub exit_code: i32,
    pub output: String,
}

impl ShellOutput {
    pub fn failed(&self) -> bool {
        self.exit_code != 0
    }
    pub fn succeeded(&self) -> bool {
        self.exit_code == 0
    }
    pub fn is_windows_restart_code(&self) -> bool {
        self.exit_code == 2359302 || self.exit_code == 3010 || self.exit_code == 0
    }
}

#[derive(Debug, Default, PartialEq, Eq, Clone)]
pub enum ShellType {
    Sh,
    #[default]
    Cmd,
    Powershell,
}

#[derive(Debug, Default)]
pub struct ShellCommand<'a> {
    command: &'a str,
    shell_type: ShellType,
    working_directory: Option<&'a Path>,
    output_file: Option<&'a Path>,
    script_file: Option<&'a Path>,
}

impl<'a> ShellCommand<'a> {
    pub fn new(command: &'a str) -> Self {
        Self {
            command,
            ..Default::default()
        }
    }

    pub fn with_consumed_type(mut self, shell_type: ShellType) -> Self {
        self.shell_type = shell_type;

        self
    }

    pub fn with_type(&mut self, shell_type: ShellType) -> &mut Self {
        self.shell_type = shell_type;

        self
    }

    pub fn cwd(&mut self, cwd: &'a Path) -> &mut Self {
        self.working_directory = Some(cwd);

        self
    }

    pub fn with_output_file(&mut self, output: &'a Path) -> &mut Self {
        self.output_file = Some(output);

        self
    }

    pub fn use_script(&mut self, script: &'a Path) -> &mut Self {
        self.script_file = Some(script);

        self
    }

    async fn write_output_to_file(&self, shell_output: &ShellOutput) {
        let mut output_file = None;

        if let Some(given_file) = self.output_file {
            match OpenOptions::new()
                .create(true)
                .append(true)
                .open(given_file)
                .await
            {
                Ok(file) => output_file = Some(file),
                Err(error) => {
                    error!(?error, "Failed to open output file to log command output");
                    output_file = None;
                }
            }
        }

        if output_file.is_some() {
            if let Err(error) = output_file
                .as_mut()
                .unwrap()
                .write_all(shell_output.output.as_bytes())
                .await
            {
                error!(?error, "Failed to write to output file");
            }
        }
    }

    fn build_script_command(&self) -> (String, String, ShellType) {
        if let Some(script_path) = self.script_file {
            let ext = script_path
                .extension()
                .unwrap_or_default()
                .to_str()
                .unwrap_or_default();

            let script = script_path.display().to_string();

            match ext {
                "bat" => (
                    "cmd.exe".to_owned(),
                    format!("\"{}\"", script),
                    ShellType::Cmd,
                ),
                "reg" => (
                    "regedit.exe".to_owned(),
                    format!("/s /q \"{}\"", script),
                    ShellType::Cmd,
                ),
                "vbs" | "vbe" => (
                    "cscript.exe".to_owned(),
                    format!("\"{}\"", script),
                    ShellType::Cmd,
                ),
                "wsf" | "wsc" => (
                    "wscript.exe".to_owned(),
                    format!("\"{}\"", script),
                    ShellType::Cmd,
                ),
                "ps1" | "ps1xml" | "psm1" => (
                    "powershell.exe".to_owned(),
                    format!("-ExecutionPolicy Bypass -File \"{}\"", script),
                    ShellType::Powershell,
                ),
                "cab" => (
                    "DISM.exe".to_owned(),
                    format!("/Online /Add-Package /PackagePath:\"{}\"", script),
                    ShellType::Cmd,
                ),
                _ => {
                    cfg_if! {
                        if #[cfg(windows)] {
                            error!(
                                "On windows platform script with extension {} is not supported!",
                                ext
                            );
                            return (
                                format!(
                                    "On windows platform script with extension {} is not supported!",
                                    ext
                                )
                                .to_owned(),
                                "".to_owned(),
                                ShellType::Cmd,
                            );
                        } else {
                            ("sh".to_owned(), format!("'{}'",script), ShellType::Sh)
                        }
                    }
                }
            }
        } else {
            error!("Called build script command without specifying script");
            ("".to_string(), "".to_string(), ShellType::Cmd)
        }
    }

    pub async fn run(&mut self) -> Result<ShellOutput, ShellError> {
        let mut shell_output = ShellOutput::default();

        let default_working_dir = get_current_dir();

        let cwd = if let Some(working_dir) = self.working_directory {
            working_dir
        } else {
            default_working_dir.as_ref()
        };

        if self.script_file.is_some() {
            #[cfg(not(target_os = "windows"))]
            {
                use std::fs::{set_permissions, Permissions};
                use std::os::unix::fs::PermissionsExt;

                set_permissions(
                    self.script_file.as_ref().unwrap(),
                    Permissions::from_mode(0o777),
                )?;
            }
        }

        let mut cmd;

        if self.script_file.is_some() {
            let (script_command, script_args, shell_type) = self.build_script_command();
            self.shell_type = shell_type;
            debug!(
                "Executing command {} {} with shell type {:?}",
                script_command, script_args, self.shell_type
            );
            cmd = Command::new(&script_command);
            #[cfg(windows)]
            {
                cmd.raw_arg(&script_args);
            }
            #[cfg(unix)]
            {
                cmd.arg(&script_args);
            }
        } else {
            if self.shell_type == ShellType::Sh || cfg!(unix) {
                cmd = Command::new("sh");
                cmd.arg("-c").arg(&self.command);
            } else if self.shell_type == ShellType::Powershell {
                cmd = Command::new("powershell.exe");
                cmd.arg("-Command");
                #[cfg(windows)]
                {
                    cmd.raw_arg(&self.command);
                }
                #[cfg(unix)]
                {
                    cmd.arg(&self.command);
                }
            } else {
                // let (command, args) = parse_cmd(&self.command);
                cmd = Command::new("cmd.exe");
                cmd.arg("/c");
                #[cfg(windows)]
                {
                    // debug!("Executing command {} with args {:?}", command, args);
                    cmd.raw_arg(&self.command);
                }
                #[cfg(unix)]
                {
                    cmd.arg(&self.command);
                }
            }
        }

        cmd.stdin(Stdio::null()).current_dir(cwd);

        debug!("Executing command {:?}", self);

        let result = cmd.output().await;

        match result {
            Ok(child) => {
                shell_output.output = from_utf8(&child.stdout)
                    .unwrap_or_default()
                    .trim()
                    .to_string();
                shell_output.exit_code = child.status.code().unwrap_or_else(|| 99);
                self.write_output_to_file(&shell_output).await;
            }
            Err(error) => {
                error!(?error, "Failed to run command {:?}", self,);
                shell_output.output = format!(
                    "Failed to run command {:?} with error {:?}",
                    self.command, error
                )
                .to_owned();
                shell_output.exit_code = 99;
            }
        };

        Ok(shell_output)
    }
}

pub fn execute_command() -> Result<ShellOutput, ShellError> {
    Ok(ShellOutput::default())
}
