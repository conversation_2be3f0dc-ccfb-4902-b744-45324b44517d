use logroller::LogRollerError;
use std::io;
use thiserror::Error;
use time::error::InvalidFormatDescription;
use tracing::subscriber::SetGlobalDefaultError;
use tracing_appender::rolling::InitError;

#[derive(Error, Debug)]
pub enum LoggerError {
    #[error("Log Error: {0:?}")]
    LogError(String),

    #[error("Log Error: {0:?}")]
    FsError(#[from] io::Error),

    #[error("Log init Error: {0:?}")]
    InitError(#[from] InitError),

    #[error("Log Roller init Error: {0:?}")]
    InitRollerError(#[from] LogRollerError),

    #[error("Log init Error: Given Time format {0:?} is not valid")]
    InvalidTimeFormat(#[from] InvalidFormatDescription),

    #[error("Log Error: Failed to set subscriber globally {0:?}")]
    SetGlobalError(#[from] SetGlobalDefaultError),
}
