use agent_manager::Agent<PERSON>unnable;
use anyhow::Result;
use async_trait::async_trait;
use database::models::{AgentMetadata, Task};
use logger::{debug, error, info, ModuleLogger, WithSubscriber};
use std::sync::Arc;
use task_execution::build_executable_task;
use tokio::select;
use tokio::sync::mpsc::{unbounded_channel, UnboundedReceiver, UnboundedSender};
use tokio::sync::Mutex;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tokio_stream::wrappers::UnboundedReceiverStream;
use tokio_stream::StreamExt;
use utils::shutdown::get_shutdown_signal;

pub struct TaskExecutionAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    priority_queue_receiver: Arc<Mutex<UnboundedReceiverStream<Task>>>,
    secondary_queue_receiver: Arc<Mutex<UnboundedReceiverStream<Task>>>,
    primary_tx: UnboundedSender<Task>,
    secondary_tx: UnboundedSender<Task>,
    task_receiver_rx: Mutex<UnboundedReceiverStream<Task>>,
}

impl<'a> TaskExecutionAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        task_receiver_rx: UnboundedReceiver<Task>,
    ) -> TaskExecutionAgent<'a> {
        let (primary_tx, primary_rx) = unbounded_channel();
        let (secondary_tx, secondary_rx) = unbounded_channel();

        TaskExecutionAgent {
            agent_metadata,
            primary_tx,
            secondary_tx,
            priority_queue_receiver: Arc::new(Mutex::new(UnboundedReceiverStream::new(primary_rx))),
            secondary_queue_receiver: Arc::new(Mutex::new(UnboundedReceiverStream::new(
                secondary_rx,
            ))),
            task_receiver_rx: Mutex::new(UnboundedReceiverStream::new(task_receiver_rx)),
        }
    }

    fn spawn_primary_queue(&self, logger: Arc<ModuleLogger>) -> JoinHandle<()> {
        let primary_rx = self.priority_queue_receiver.clone();
        let agent_metadata = self.agent_metadata.clone();
        tokio::task::spawn(
            async move {
                let mut primary_rx = primary_rx.lock().await;
                while let Some(task) = primary_rx.next().await {
                    debug!("Received task {:?} in primary queue", task);
                    let agent_metadata = agent_metadata.clone();
                    match build_executable_task(task, agent_metadata) {
                        Ok(mut executable) => {
                            let task_logger = executable.logger();
                            let result = executable
                                .run()
                                .with_subscriber(task_logger.subscriber())
                                .await;
                            debug!("Task finished with result {:?}", result);
                            drop(executable);
                        }
                        Err(error) => {
                            error!(?error, "Failed to build task executable from message");
                        }
                    };
                }
            }
            .with_subscriber(logger.subscriber()),
        )
    }

    fn spawn_secondary_queue(&self, logger: Arc<ModuleLogger>) -> JoinHandle<()> {
        let secondary_rx = self.secondary_queue_receiver.clone();
        let agent_metadata = self.agent_metadata.clone();
        tokio::task::spawn(
            async move {
                let mut secondary_rx = secondary_rx.lock().await;
                while let Some(task) = secondary_rx.next().await {
                    debug!("Received task {:?} in secondary queue", task);
                    let agent_metadata = agent_metadata.clone();
                    match build_executable_task(task, agent_metadata) {
                        Ok(mut executable) => {
                            let task_logger = executable.logger();
                            let result = executable
                                .run()
                                .with_subscriber(task_logger.subscriber())
                                .await;
                            debug!("Task finished with result {:?}", result);
                            drop(executable);
                        }
                        Err(error) => {
                            error!(?error, "Failed to build task executable from message");
                        }
                    };
                }
            }
            .with_subscriber(logger.subscriber()),
        )
    }

    pub fn add(&self, logger: Arc<ModuleLogger>, task: Task) {
        logger.with(|| {
            let name = task.name.to_owned();
            if task.should_put_in_primary_queue() {
                match self.primary_tx.send(task) {
                    Ok(_) => debug!(
                        "Published task {} to primary queue",
                        name.unwrap_or_default()
                    ),
                    Err(error) => {
                        error!(?error, "Failed to publish task to primary queue");
                    }
                }
            } else {
                match self.secondary_tx.send(task) {
                    Ok(_) => debug!(
                        "Published task {} to secondary queue",
                        name.unwrap_or_default()
                    ),
                    Err(error) => {
                        error!(?error, "Failed to publish secondary to primary queue");
                    }
                }
            }
        })
    }
}

#[async_trait]
impl AgentRunnable for TaskExecutionAgent<'static> {
    fn get_name(&self) -> &str {
        "task_execution_agent"
    }

    async fn start(&self, logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting Task Execution Agent ------------------------");
        let _agent_config = self.agent_metadata.get_config();

        let mut task_receiver_stream = self.task_receiver_rx.lock().await;

        let mut primary_queue = self.spawn_primary_queue(logger.clone());
        let mut secondary_queue = self.spawn_secondary_queue(logger.clone());

        let mut shutdown_signal = get_shutdown_signal();

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Task Execution Agent");
                    break;
                },

                _ = &mut primary_queue => {
                    info!("Primary task queue has been finished exeecuting");
                }

                _ = &mut secondary_queue => {
                    info!("Secondary task queue has been finished exeecuting");
                }

                message = task_receiver_stream.next() => {
                    if let Some(task) = message {
                        self.add(logger.clone(), task);
                    }
                }

            }
        }
        info!("---------------------- Stopped Task Receiver Agent ------------------------");
        Ok(())
    }
}
