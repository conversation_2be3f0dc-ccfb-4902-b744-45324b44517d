use agent_manager::AgentRunna<PERSON>;
use anyhow::Result;
use api::patch::{trigger_patch_scan, trigger_redhat_nomination_task};
use api::task::change_task_only_status;
use async_trait::async_trait;
use database::is_system_rebooted_recently;
use database::models::{
    AgentMetadata, PatchScanHistory, RedhatNominationHistory, ServerConfig, Task, TaskStatus,
};
use logger::{debug, error};
use logger::{info, ModuleLogger};
use std::sync::Arc;
use std::time::Duration;
use task_execution::build_executable_task;
use tokio::select;
use tokio::sync::mpsc::UnboundedSender;
use tokio::time::interval;
use utils::shutdown::get_shutdown_signal;

pub struct ScheduledTaskAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    task_sender: UnboundedSender<Task>,
    server_config: &'a ServerConfig,
}

impl<'a> ScheduledTaskAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        task_sender: UnboundedSender<Task>,
        server_config: &'a ServerConfig,
    ) -> ScheduledTaskAgent<'a> {
        ScheduledTaskAgent {
            agent_metadata,
            task_sender,
            server_config,
        }
    }

    async fn check_and_trigger_patch_scan(&self) -> Result<()> {
        let patch_refresh_cycle: i64;
        let time_diff: i64;
        // check if last patch scan trigger time and should trigger now
        if let Some(record) = PatchScanHistory::get_last_scanned_record().await {
            debug!("Last patch scan record {:?}", record);
            time_diff = chrono::Utc::now().timestamp() - record.last_scanned_at();
            patch_refresh_cycle = self
                .agent_metadata
                .get_config()
                .agent_refresh_time_settings
                .patch_refresh_cycle as i64;
        } else {
            let installed_at = self.server_config.created_at();
            debug!("Installed at {:?}", installed_at);
            time_diff = chrono::Utc::now().timestamp() - installed_at;
            patch_refresh_cycle = 3 * 60 * 60;
        }
        if time_diff >= patch_refresh_cycle {
            info!("Last scan is {} seconds ago and patch scan refresh cycle is {} so triggering patch scan", time_diff, patch_refresh_cycle);
            if let Err(error) = trigger_patch_scan(self.agent_metadata.get_endpoint_id()).await {
                error!(?error, "Failed to trigger patch scan api");
                return Err(error.into());
            }
        }
        Ok(())
    }

    async fn check_and_trigger_redhat_nomination_task(&self) -> Result<()> {
        // check if last redhat nomination task trigger time and should trigger now
        if let Some(record) = RedhatNominationHistory::get_last_performed_record().await {
            debug!("Last nomination task record {:?}", record);
            let next_run_at = record.last_performed_at() + record.next_run_remaining_sec;
            let now = chrono::Utc::now().timestamp();
            // if current time is greater than or equal to trigger time then we need to trigger now
            if now >= next_run_at {
                info!("Nomination task Last performed at {} next run at is {} which is lesser so triggering now", record.last_performed_at(), next_run_at);
                if let Err(error) =
                    trigger_redhat_nomination_task(self.agent_metadata.get_endpoint_id()).await
                {
                    error!(?error, "Failed to trigger redhat nomination task api");
                    return Err(error.into());
                }
            }
            return Ok(());
        } else {
            return Ok(());
        }
    }
}

#[async_trait]
impl AgentRunnable for ScheduledTaskAgent<'static> {
    fn get_name(&self) -> &str {
        "scheduled_task_agent"
    }

    async fn start(&self, _logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting Scheduled Task Agent ------------------------");

        let mut shutdown_signal = get_shutdown_signal();
        let mut interval = interval(Duration::from_secs(60));

        // check if system is recently booted and task with success with reboot is in db
        if is_system_rebooted_recently() {
            let tasks = Task::get_reboot_required_tasks().await;
            if tasks.len() > 0 {
                info!(
                    "Selected total {} tasks with success reboot status to be marked as success",
                    tasks.len()
                );

                for task in tasks {
                    let task_id = task.id.to_i64();
                    match build_executable_task(task, self.agent_metadata.clone()) {
                        Ok(executable) => {
                            if change_task_only_status(vec![task_id], TaskStatus::Success)
                                .await
                                .is_ok()
                            {
                                if let Err(error) = executable.remove_task_resources().await {
                                    error!(
                                        "Failed to remove task resources for task {} with error {:?}",
                                        self.get_name(),
                                        error
                                    );
                                }
                            }
                            drop(executable);
                        }
                        Err(error) => {
                            error!(?error, "Failed to build task executable from message");
                        }
                    };
                }
            }
        }

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Scheduled Task Agent");
                    break;
                }

                _ = interval.tick() => {
                    debug!("Checking for scheduled tasks");
                    let tasks: Vec<Task> = Task::get_pending_tasks().await;
                    if tasks.len() > 0 {
                        info!("Selected total {} tasks", tasks.len());

                        for task in tasks.into_iter() {
                            debug!("Processing task {:?}", task);
                            if let Err(error) = self.task_sender.send(task) {
                                error!(?error, "Failed to send task to task receiver {:?}", error.0);
                            }
                        }
                    }
                    self.check_and_trigger_patch_scan().await.ok();
                    self.check_and_trigger_redhat_nomination_task().await.ok();
                },
            }
        }
        info!("---------------------- Stopped Scheduled Task Agent ------------------------");
        Ok(())
    }
}
