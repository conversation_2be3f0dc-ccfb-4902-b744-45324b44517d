use std::sync::Arc;

use agent_manager::AgentRunnable;
use anyhow::{anyhow, Result};
use async_trait::async_trait;
use logger::{error, info, ModuleLogger};
use tokio::process::Command;
use tokio::select;
use utils::constants::{MESH_AGENT_BINARY_NAME, MESH_AGENT_SERVICE_NAME};
use utils::dir::get_current_dir;
use utils::service_manager::{ServiceManager, ServiceStatus};
use utils::shutdown::get_shutdown_signal;

pub struct RdpAgent;

impl RdpAgent {
    pub fn new() -> RdpAgent {
        RdpAgent
    }

    async fn install_rdp_service(&self, service_manager: &ServiceManager) -> Result<()> {
        match service_manager.status() {
            Ok(status) => {
                if status == ServiceStatus::NotInstalled {
                    match Command::new(service_manager.get_program().to_string_lossy().to_string())
                        .arg("-install")
                        .arg(format!("--installPath={}", get_current_dir().display()))
                        .output()
                        .await
                    {
                        Ok(output) => {
                            info!(
                                "Got service installation output as {}",
                                String::from_utf8_lossy(&output.stdout)
                            );
                            return Ok(());
                        }
                        Err(error) => {
                            error!(?error, "Failed to install remote desktop service");
                            return Err(anyhow!("Failed to install remote desktop service"));
                        }
                    };
                } else {
                    Ok(())
                }
            }
            Err(error) => {
                error!(?error, "Failed to get service status");
                return Err(anyhow!("Failed to get service status"));
            }
        }
    }

    fn start_rdp_service(&self, service_manager: &ServiceManager) -> Result<()> {
        match service_manager.status() {
            Ok(status) => {
                if status == ServiceStatus::Running {
                    info!("RDP Service is already running so skipping start");
                    return Ok(());
                } else {
                    info!("Starting RDP Service");
                    service_manager.start()
                }
            }
            Err(error) => {
                error!(?error, "Failed to get service status");
                return Err(anyhow!("Failed to get service status"));
            }
        }
    }

    fn stop_rdp_service(&self, service_manager: &ServiceManager) -> Result<()> {
        match service_manager.status() {
            Ok(status) => {
                if status == ServiceStatus::Running {
                    info!("Stopping RDP Service");
                    service_manager.stop()
                } else {
                    info!("RDP Service is not running so skipping stop");
                    return Ok(());
                }
            }
            Err(error) => {
                error!(?error, "Failed to get service status");
                return Err(anyhow!("Failed to get service status"));
            }
        }
    }
}

#[async_trait]
impl AgentRunnable for RdpAgent {
    fn get_name(&self) -> &str {
        "rdp_agent"
    }

    async fn start(&self, _logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting RDP Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();
        let service_label = MESH_AGENT_SERVICE_NAME;

        let exe_path = get_current_dir().join(MESH_AGENT_BINARY_NAME);

        if exe_path.exists() == false {
            error!("RDP Agent binary not found at {}", exe_path.display());
            shutdown_signal.recv().await.ok();
            return Err(anyhow!("RDP Agent binary not found"));
        }

        let service_manager =
            ServiceManager::new(service_label.to_owned(), exe_path.clone(), None, vec![]);

        if let Err(error) = self.install_rdp_service(&service_manager).await {
            error!(?error, "Failed to install rdp service");
            shutdown_signal.recv().await.ok();
            return Err(anyhow!("Failed to install rdp service"));
        }

        #[cfg(windows)]
        {
            use winreg::enums::*;
            use winreg::RegKey;
            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            let path = r"Software\Microsoft\Windows\CurrentVersion\Uninstall";
            match hklm
                .open_subkey_with_flags(format!("{}\\{}", path, MESH_AGENT_SERVICE_NAME), KEY_WRITE)
            {
                Ok(key) => {
                    match key.set_value("DisplayVersion", &env!("CARGO_PKG_VERSION")) {
                        Err(error) => {
                            error!(?error, "Failed to set DisplayVersion key");
                        }
                        _ => {}
                    };
                }
                Err(error) => {
                    error!(?error, "Failed to open registry key {}", path);
                }
            }
        }

        if let Err(error) = self.start_rdp_service(&service_manager) {
            error!(?error, "Failed to start rdp service");
            shutdown_signal.recv().await.ok();
            return Err(anyhow!("Failed to start rdp service"));
        }

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down RDP Agent");
                    if let Err(error) = self.stop_rdp_service(&service_manager) {
                        error!(?error, "Failed to start rdp service");
                        return Err(anyhow!("Failed to start rdp service"));
                    }
                    break;
                }
            }
        }
        info!("---------------------- Stopped RDP Agent ------------------------");
        Ok(())
    }
}
