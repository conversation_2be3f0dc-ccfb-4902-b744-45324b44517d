use agent_manager::Agent<PERSON>un<PERSON><PERSON>;
use anyhow::Result;
use api::agent::request_enrollment;
use async_trait::async_trait;
use database::models::{EnrollmentStatus, ServerConfig};
use logger::{debug, error, info, trace, ModuleLogger};
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::time::interval;
use utils::shutdown::get_shutdown_signal;

pub struct ApprovalAgent<'a> {
    server_config: &'a ServerConfig,
}

impl<'a> ApprovalAgent<'a> {
    pub fn new(server_config: &'a ServerConfig) -> ApprovalAgent<'a> {
        ApprovalAgent { server_config }
    }
}

#[async_trait]
impl AgentRunnable for ApprovalAgent<'static> {
    fn get_name(&self) -> &str {
        "approval_agent"
    }

    async fn start(&self, _logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting Approval Agent ------------------------");
        let mut shutdown_receiver = get_shutdown_signal();

        let mut interval = interval(Duration::from_secs(10));
        loop {
            select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    info!("Shutting Down Approval Agent");
                    break;
                }

                _ = interval.tick() => {
                    match request_enrollment(self.server_config).await {
                        Ok(result) => {
                            trace!("Got Agent approval status {:?}", result);
                            if result == EnrollmentStatus::Approved {
                                debug!("Got agent approval status as approved");
                                break;
                            }
                        }
                        Err(error) => {
                            error!(?error, "Failed to check enrollment request status");
                        }
                    };
                }


            }
        }
        info!("---------------------- Stopped Approval Agent ------------------------");
        Ok(())
    }
}
