use agent_manager::Agent<PERSON><PERSON><PERSON><PERSON>;
use anyhow::Result;
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use auth_event::{get_default_monitor, AuthEvent};
use database::{models::AgentMetadata, Model};
use logger::{debug, error, info, ModuleLogger, WithSubscriber};
use serde_json::json;
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tokio::time::sleep;
use utils::shutdown::get_shutdown_signal;

pub struct AuthEventAgent<'a> {
    agent_metadata: &'a AgentMetadata,
}

impl AuthEventAgent<'static> {
    pub fn new(agent_metadata: &'static AgentMetadata) -> AuthEventAgent<'static> {
        AuthEventAgent { agent_metadata }
    }

    async fn send_all_pending_events(agent_metadata: &AgentMetadata) {
        loop {
            match AuthEvent::default().get_all(Some(100)).await {
                Ok(items) => {
                    let found_items = items.len();
                    if found_items > 0 {
                        if let Err(error) = send_fim_data(json!({
                                "asset_id" : agent_metadata.get_endpoint_id(),
                                "data" : json!({
                                    "auth_events": items,
                                })
                        }))
                        .await
                        {
                            error!(
                                ?error,
                                "Failed to send {} events to server and will be added back to",
                                found_items
                            );
                        } else {
                            match AuthEvent::default()
                                .delete_selected(items.into_iter().collect())
                                .await
                            {
                                Ok(data) => {
                                    debug!(
                                        "Deleted {} records from database {:?}",
                                        found_items, data
                                    )
                                }
                                Err(error) => {
                                    error!(
                                        ?error,
                                        "Failed to delete {} records from database", found_items
                                    )
                                }
                            };
                            debug!("Sent total {} events to server", found_items);
                        }
                    } else {
                        debug!("Sent All pending data to server");
                        break;
                    }
                }
                Err(error) => {
                    error!(?error, "Failed to fetch all fs events");
                    break;
                }
            };
        }
    }

    fn spawn_sync_task(
        logger: &Arc<ModuleLogger>,
        agent_metadata: &AgentMetadata,
        mut rx: Receiver<AuthEvent>,
    ) -> JoinHandle<()> {
        let endpoint_id = agent_metadata.get_endpoint_id();
        tokio::task::spawn(
            async move {
                while let Some(event) = rx.recv().await {
                    if let Err(error) = send_fim_data(json!({
                            "asset_id" : endpoint_id,
                            "data" : json!({
                                "auth_events": [event],
                            })
                    }))
                    .await
                    {
                        if let Err(error) = event.persist().await {
                            error!(?error, "Failed to unsend auth event to database");
                        };
                        error!(?error, "Failed to sync login event");
                    } else {
                        debug!("Sent Event {:?} to server", event);
                    }
                }
            }
            .with_subscriber(logger.subscriber()),
        )
    }

    fn spawn_monitor_task(logger: &Arc<ModuleLogger>, tx: Sender<AuthEvent>) -> JoinHandle<()> {
        tokio::task::spawn(
            async {
                match get_default_monitor().monitor(tx).await {
                    Ok(_) => {
                        debug!("Finished running monitor");
                    }
                    Err(error) => {
                        error!(?error, "Failed to monitor auth events");
                    }
                };
            }
            .with_subscriber(logger.subscriber()),
        )
    }
}

#[async_trait]
impl AgentRunnable for AuthEventAgent<'static> {
    fn get_name(&self) -> &str {
        "auth_events_agent"
    }

    async fn start(&self, logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting Auth Events Agent ------------------------");

        let mut shutdown_signal = get_shutdown_signal();

        let (tx, rx) = channel(10);

        let mut sync_task = AuthEventAgent::spawn_sync_task(&logger, self.agent_metadata, rx);

        let mut monitor_task = AuthEventAgent::spawn_monitor_task(&logger, tx);

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Auth Events Agent");
                    break;
                },

                _ = &mut sync_task => {
                    info!("Event Sync task is finished executing");
                }

                _ = &mut monitor_task => {
                    info!("Event Monitor task is finished executing");
                }

                _ = sleep(Duration::from_secs(120)) => {
                    debug!("Checking for pending file events");
                    AuthEventAgent::send_all_pending_events(self.agent_metadata).await;
                }

            }
        }
        info!("---------------------- Stopped Auth Events Agent ------------------------");
        Ok(())
    }
}
