use agent_manager::Agent<PERSON>unna<PERSON>;
use anyhow::Result;
use api::agent::refresh_call;
use api::task::change_task_status;
use async_trait::async_trait;
use database::data_types::TaskResult;
use database::models::{AgentMetadata, Task, TaskStatus};
use database::Model;
use logger::{debug, error};
use logger::{info, ModuleLogger};
use std::cmp;
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::sync::mpsc::UnboundedSender;
use tokio::time::sleep;
use utils::shutdown::get_shutdown_signal;

pub struct RefreshCallAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    task_sender: UnboundedSender<Task>,
}

impl<'a> RefreshCallAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        task_sender: UnboundedSender<Task>,
    ) -> RefreshCallAgent<'a> {
        RefreshCallAgent {
            agent_metadata,
            task_sender,
        }
    }

    async fn process_refresh_call_response(&self, tasks: Vec<Task>) {
        let ids_to_change = tasks
            .iter()
            .filter(|task| !task.is_system_action_task())
            .map(|task| task.id.to_i64())
            .collect::<Vec<i64>>();

        if ids_to_change.len() > 0 {
            match change_task_status(
                &ids_to_change,
                &TaskResult {
                    status: TaskStatus::Initiated,
                    ..Default::default()
                },
            )
            .await
            {
                Ok(_) => {
                    debug!(
                        "Changed status of tasks {:?} to initiated successfully",
                        ids_to_change
                    )
                }
                Err(error) => {
                    error!(?error, "Failed to change status of task to initiated");
                }
            }
        }

        for mut task in tasks {
            if task.should_persist() {
                task.status = if task.is_instant_deployment() {
                    TaskStatus::InProgress
                } else {
                    TaskStatus::Initiated
                };
                if let Some(deployment) = task.deployment.as_ref() {
                    task.retry_count = Some(cmp::max(deployment.retry_count, 1));
                }
                match task.persist().await {
                    Ok(task) => {
                        if task.is_scheduled_task() == false {
                            // if not scheduled task then send to task receiver for execution
                            if let Err(error) = self.task_sender.send(task) {
                                error!(
                                    ?error,
                                    "Failed to send task to task receiver {:?}", error.0
                                );
                            }
                        }
                    }
                    Err(error) => {
                        error!(?error, "Failed to persist task in the database {:?}", task);
                    }
                };
            } else {
                if let Err(error) = self.task_sender.send(task) {
                    error!(?error, "Failed to send task to task receiver {:?}", error.0);
                }
            };
        }
    }
}

#[async_trait]
impl AgentRunnable for RefreshCallAgent<'static> {
    fn get_name(&self) -> &str {
        "refresh_call_agent"
    }

    async fn start(&self, _logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting Refresh Call Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Refresh Call Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(
                    self.agent_metadata.get_agent_refresh_settings().system_action_refresh_cycle,
                )) => {
                    // check refresh call
                    let endpoint_id = self.agent_metadata.get_endpoint_id();
                    match refresh_call(endpoint_id).await {
                        Ok(response) => {
                            debug!("Refresh call Response {:?}", response);
                            self.process_refresh_call_response(response.tasks.unwrap_or_default()).await;
                        },
                        Err(error) => {
                            error!(?error, "Failed to finish refresh call api");
                        }
                    }
                },
            }
        }
        info!("---------------------- Stopped Refresh Call Agent ------------------------");
        Ok(())
    }
}
