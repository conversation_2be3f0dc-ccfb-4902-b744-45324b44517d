use agent_manager::AgentRunnable;
use anyhow::Result;
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use database::{models::AgentMetadata, Model};
use fim::FIMEvent;
use logger::{debug, error, ModuleLogger};
use logger::{info, trace};
use serde_json::json;
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::time::sleep;
use utils::shutdown::{get_shutdown_signal, is_system_running};

pub struct FIMDataSyncAgent<'a> {
    agent_metadata: &'a AgentMetadata,
}

impl<'a> FIMDataSyncAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> FIMDataSyncAgent<'a> {
        FIMDataSyncAgent { agent_metadata }
    }

    async fn send_all_pending_events(agent_metadata: &AgentMetadata) {
        loop {
            if !is_system_running() {
                break;
            }
            let pending_events = FIMEvent::default().get_pending_events(50).await;

            if pending_events.len() == 0 {
                trace!("No pending events found to send");
                break;
            }
            debug!("Found total {} pending events", pending_events.len());

            let mut events = vec![];

            for event in pending_events {
                match event.delete().await {
                    Ok(event) => {
                        events.push(event);
                    }
                    Err(error) => {
                        error!(?error, "Failed to delete event {event:?} from database");
                    }
                }
            }

            if let Err(error) = send_fim_data(json!({
                    "asset_id" : agent_metadata.get_endpoint_id(),
                    "data" : json!({
                        "fim_events": events,
                    })
            }))
            .await
            {
                error!(
                    ?error,
                    "Failed to send {} events to server and will be added back to database",
                    events.len()
                );
                let total_events_to_be_reinserted = events.len();
                let inserted_records = FIMEvent::default()
                    .bulk_insert(events.into_iter().collect())
                    .await;
                if inserted_records.len() != total_events_to_be_reinserted {
                    error!(
                        ?error,
                        "Failed to add {} events back to database this has caused event loss",
                        total_events_to_be_reinserted - inserted_records.len()
                    );
                }
            }
        }
    }
}

#[async_trait]
impl AgentRunnable for FIMDataSyncAgent<'static> {
    fn get_name(&self) -> &str {
        "fim_data_sync"
    }

    async fn start(&self, _logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting FIM Data Sync Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();

        FIMDataSyncAgent::send_all_pending_events(self.agent_metadata).await;

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Data sync Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(self.agent_metadata
                    .get_agent_refresh_settings()
                    .file_events_refresh_cycle)) => {
                    trace!("Checking for pending file events");
                    FIMDataSyncAgent::send_all_pending_events(self.agent_metadata).await;

                },
            }
        }
        info!("---------------------- Stopped FIM Data sync Agent ------------------------");
        Ok(())
    }
}
