use std::sync::Arc;

use crate::extension_agent::ExtensionAgent;
use agent_manager::{<PERSON>, Agent<PERSON>anager, AgentRunnable};
use anyhow::Result;
use async_trait::async_trait;
use data_collection::Certificates;
use data_collection::NetworkInterfaces;
use data_collection::OsServices;
use data_collection::Processes;
use data_collection::Resources;
use data_collection::Softwares;
use data_collection::StartUpItems;
use data_collection::Users;
use database::models::AgentMetadata;
use database::models::ServerProductType;
use logger::info;
use logger::ModuleLogger;
use tokio::select;
use utils::shutdown::get_shutdown_signal;

pub struct DataCollectionAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    product_type: ServerProductType,
}

impl<'a> DataCollectionAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        product_type: ServerProductType,
    ) -> DataCollectionAgent<'a> {
        DataCollectionAgent {
            agent_metadata,
            product_type,
        }
    }
}

#[async_trait]
impl AgentRunnable for DataCollectionAgent<'static> {
    fn get_name(&self) -> &str {
        "data_collection_agent"
    }

    async fn start(&self, _logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting Data Collection Agent ------------------------");

        let agent_metadata = self.agent_metadata;

        let mut agent_manager = AgentManager::default();

        // system resources agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Resources::new(agent_metadata),
        )))));

        // startup items agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            StartUpItems::new(agent_metadata),
        )))));

        // users agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Users::new(agent_metadata),
        )))));

        // software agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Softwares::new(agent_metadata),
        )))));

        if self.product_type.is_endpointops() {
            // services agent
            agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
                OsServices::new(agent_metadata),
            )))));

            // process agent
            agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
                Processes::new(agent_metadata),
            )))));

            // network interface agent
            agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
                NetworkInterfaces::new(agent_metadata),
            )))));

            // certificate agent
            agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
                Certificates::new(agent_metadata),
            )))));
        }

        let mut shutdown_signal = get_shutdown_signal();

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    // agent_manager.stop().await;
                    info!("Shutting down Data Collection Agent");
                    break;
                }

                _ = agent_manager.start() => {
                    info!("Finished running all extensions");
                }

            }
        }
        info!("---------------------- Stopped Data Collection Agent ------------------------");
        Ok(())
    }
}
