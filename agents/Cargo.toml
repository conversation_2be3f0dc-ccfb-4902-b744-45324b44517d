[package]
name = "agents"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
data_collection = { path = "../data_collection" }
agent_manager = { path = "../agent_manager" }
task_execution = { path = "../task_execution" }
utils = { path = "../utils" }
database = { path = "../database" }
api = { path = "../api" }
fim = { path = "../fim" }
auth_event = { path = "../auth_event" }
cfg-if = "1.0.1"
anyhow = { version = "1.0.98", features = ["backtrace"] }
async-trait = "0.1.88"
tokio = { version = "1.46.1", features = ["full", "tracing"] }
serde_json = "1.0.140"
sysinfo = "0.36.0"
serde = "1.0.219"
futures-util = "0.3.31"
tokio-stream = "0.1.17"
chrono = "0.4.41"

[target.'cfg(windows)'.dependencies]
winreg = "0.55.0"
