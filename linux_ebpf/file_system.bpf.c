// SPDX-License-Identifier: GPL-2.0
#include "vmlinux.h"
#include "common.bpf.h"
#include "bpf/bpf_helpers.h"
#include "buffer.bpf.h"
#include "get_path.bpf.h"
#include "interest_tracking.bpf.h"

char LICENSE[] SEC("license") = "GPL v2";

#define FILE_CREATED 0
#define FILE_DELETED 1
#define DIR_CREATED 2
#define DIR_DELETED 3
#define FILE_OPENED 4
#define FILE_LINK 5
#define FILE_RENAME 6

#define MAX_BLOCK_RULES 128
#define MAX_PATH_LEN 256
#define EPERM 1

struct block_rule {
    __u32 uid;           // 0xFFFFFFFF = all users
    char path[MAX_PATH_LEN];
};

struct file_opened_event {
    struct buffer_index filename;
    int flags;
};

struct file_link_event {
    struct buffer_index source;
    struct buffer_index destination;
    bool hard_link;
};

struct file_rename_event {
    struct buffer_index source;
    struct buffer_index destination;
};

GLOBAL_INTEREST_MAP_DECLARATION;

OUTPUT_MAP(fs_event, {
    struct buffer_index created;
    struct buffer_index deleted;
    struct buffer_index dir_created;
    struct buffer_index dir_deleted;
    struct file_opened_event opened;
    struct file_link_event link;
    struct file_rename_event rename;
});

// BPF map for storing block rules (uid + path combinations)
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, MAX_BLOCK_RULES);
    __type(key, __u32);
    __type(value, struct block_rule);
} block_rules SEC(".maps");

// Utility to build a struct path from a dentry + parent path
static __always_inline struct path make_path(struct dentry *target_dentry,
                                             struct path *parent_path) {
    struct path target_path = {
        .dentry = target_dentry,
        .mnt = BPF_CORE_READ(parent_path, mnt),
    };
    return target_path;
}

// Helper function to check if a path should be blocked for a given user
// This uses a simplified approach - checking only the filename, not the full path
static __always_inline bool should_block_access(struct path *path, __u32 uid) {
    struct block_rule *rule;
    __u32 key;

    // Get the dentry name for simple comparison
    struct dentry *dentry = BPF_CORE_READ(path, dentry);
    if (!dentry) {
        return false;
    }

    struct qstr d_name = BPF_CORE_READ(dentry, d_name);
    const unsigned char *name = d_name.name;
    if (!name) {
        return false;
    }

    // Check all block rules
    for (key = 0; key < MAX_BLOCK_RULES; key++) {
        rule = bpf_map_lookup_elem(&block_rules, &key);
        if (!rule) {
            continue;
        }

        // Skip empty rules (path[0] == 0)
        if (rule->path[0] == 0) {
            continue;
        }

        // Check if rule applies to this user (0xFFFFFFFF means all users)
        if (rule->uid != 0xFFFFFFFF && rule->uid != uid) {
            continue;
        }

        // Simple string comparison - check if the filename matches the rule path
        // This is a simplified version - in practice you'd want full path matching
        char filename[64] = {0};
        if (bpf_probe_read_kernel_str(filename, sizeof(filename), (const char *)name) > 0) {
            // Simple prefix match
            int i;
            for (i = 0; i < 63 && i < MAX_PATH_LEN - 1; i++) {
                if (rule->path[i] == 0) {
                    // End of rule path - this is a match
                    return true;
                }
                if (filename[i] == 0) {
                    // End of filename but rule continues - no match
                    break;
                }
                if (filename[i] != rule->path[i]) {
                    // Characters don't match - no match
                    break;
                }
            }

            // If we've reached the end of both strings or rule path, it's a match
            if (i == 63 || rule->path[i] == 0) {
                return true;
            }
        }
    }

    return false;
}

/* --------------------
 * Event Handlers
 * -------------------- */

static __always_inline void on_path_mknod(void *ctx, struct path *dir,
                                          struct dentry *dentry, umode_t mode,
                                          unsigned int dev) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path path = make_path(dentry, dir);
    struct fs_event *event = init_fs_event(FILE_CREATED, tgid);
    if (!event)
        return;
    get_path_str(&path, &event->buffer, &event->created);
    output_fs_event(ctx, event);
}

static __always_inline void on_path_unlink(void *ctx, struct path *dir,
                                           struct dentry *dentry) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path path = make_path(dentry, dir);
    struct fs_event *event = init_fs_event(FILE_DELETED, tgid);
    if (!event)
        return;
    get_path_str(&path, &event->buffer, &event->deleted);
    output_fs_event(ctx, event);
}

static __always_inline void on_file_open(void *ctx, struct file *file) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path path = BPF_CORE_READ(file, f_path);
    struct fs_event *event = init_fs_event(FILE_OPENED, tgid);
    if (!event)
        return;
    get_path_str(&path, &event->buffer, &event->opened.filename);
    event->opened.flags = BPF_CORE_READ(file, f_flags);
    output_fs_event(ctx, event);
}

static __always_inline void on_path_link(void *ctx, struct dentry *old_dentry,
                                         struct path *new_dir,
                                         struct dentry *new_dentry) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path source = make_path(new_dentry, new_dir);
    struct path destination = make_path(old_dentry, new_dir);
    struct fs_event *event = init_fs_event(FILE_LINK, tgid);
    if (!event)
        return;
    get_path_str(&source, &event->buffer, &event->link.source);
    get_path_str(&destination, &event->buffer, &event->link.destination);
    event->link.hard_link = true;
    output_fs_event(ctx, event);
}

static __always_inline void on_path_symlink(void *ctx, struct path *dir,
                                            struct dentry *dentry,
                                            char *old_name) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path path = make_path(dentry, dir);
    struct fs_event *event = init_fs_event(FILE_LINK, tgid);
    if (!event)
        return;
    get_path_str(&path, &event->buffer, &event->link.source);
    buffer_index_init(&event->buffer, &event->link.destination);
    buffer_append_str(&event->buffer, &event->link.destination, old_name,
                      BUFFER_MAX, 0);
    event->link.hard_link = false;
    output_fs_event(ctx, event);
}

static __always_inline void on_path_mkdir(void *ctx, struct path *dir,
                                          struct dentry *dentry, umode_t mode) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path path = make_path(dentry, dir);
    struct fs_event *event = init_fs_event(DIR_CREATED, tgid);
    if (!event)
        return;
    get_path_str(&path, &event->buffer, &event->dir_created);
    output_fs_event(ctx, event);
}

static __always_inline void on_path_rmdir(void *ctx, struct path *dir,
                                          struct dentry *dentry) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path path = make_path(dentry, dir);
    struct fs_event *event = init_fs_event(DIR_DELETED, tgid);
    if (!event)
        return;
    get_path_str(&path, &event->buffer, &event->dir_deleted);
    output_fs_event(ctx, event);
}

static __always_inline void on_path_rename(void *ctx, struct path *old_dir,
                                           struct dentry *old_dentry,
                                           struct path *new_dir,
                                           struct dentry *new_dentry) {
    pid_t tgid = tracker_interesting_tgid(&GLOBAL_INTEREST_MAP);
    if (tgid < 0)
        return;
    struct path source = make_path(old_dentry, old_dir);
    struct path destination = make_path(new_dentry, new_dir);
    struct fs_event *event = init_fs_event(FILE_RENAME, tgid);
    if (!event)
        return;
    get_path_str(&source, &event->buffer, &event->rename.source);
    get_path_str(&destination, &event->buffer, &event->rename.destination);
    output_fs_event(ctx, event);
}

/* --------------------
 * SEC Programs
 * -------------------- */

SEC("lsm/path_mknod")
int BPF_PROG(path_mknod, struct path *dir, struct dentry *dentry,
             umode_t mode, unsigned int dev) {
    // Get current user ID
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    // Build the target path
    struct path path = make_path(dentry, dir);

    // Check if access should be blocked
    if (should_block_access(&path, uid)) {
        return -EPERM;  // Block file creation
    }

    on_path_mknod(ctx, dir, dentry, mode, dev);
    return 0;
}

SEC("lsm/path_unlink")
int BPF_PROG(path_unlink, struct path *dir, struct dentry *dentry) {
    // Get current user ID
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    // Build the target path
    struct path path = make_path(dentry, dir);

    // Check if access should be blocked
    if (should_block_access(&path, uid)) {
        return -EPERM;  // Block file deletion
    }

    on_path_unlink(ctx, dir, dentry);
    return 0;
}

SEC("lsm/file_open")
int BPF_PROG(file_open, struct file *file) {
    // Get current user ID
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    // Get the file path
    struct path path = BPF_CORE_READ(file, f_path);

    // Check if access should be blocked
    if (should_block_access(&path, uid)) {
        return -EPERM;  // Block access
    }

    on_file_open(ctx, file);
    return 0;
}

SEC("lsm/path_link")
int BPF_PROG(path_link, struct dentry *old_dentry, struct path *new_dir,
             struct dentry *new_dentry) {
    on_path_link(ctx, old_dentry, new_dir, new_dentry);
    return 0;
}

SEC("lsm/path_symlink")
int BPF_PROG(path_symlink, struct path *dir, struct dentry *dentry,
             char *old_name) {
    on_path_symlink(ctx, dir, dentry, old_name);
    return 0;
}

SEC("lsm/path_mkdir")
int BPF_PROG(path_mkdir, struct path *dir, struct dentry *dentry,
             umode_t mode) {
    // Get current user ID
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    // Build the target path
    struct path path = make_path(dentry, dir);

    // Check if access should be blocked
    if (should_block_access(&path, uid)) {
        return -EPERM;  // Block directory creation
    }

    on_path_mkdir(ctx, dir, dentry, mode);
    return 0;
}

SEC("lsm/path_rmdir")
int BPF_PROG(path_rmdir, struct path *dir, struct dentry *dentry) {
    // Get current user ID
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    // Build the target path
    struct path path = make_path(dentry, dir);

    // Check if access should be blocked
    if (should_block_access(&path, uid)) {
        return -EPERM;  // Block directory deletion
    }

    on_path_rmdir(ctx, dir, dentry);
    return 0;
}

/* Special case for path_rename: kernel >= 5.19 has extra args */
#ifdef FEATURE_LSM
static __always_inline int shim_5_19_on_path_rename(unsigned long long *ctx,
                                                    struct path *old_dir,
                                                    struct dentry *old_dentry,
                                                    struct path *new_dir,
                                                    struct dentry *new_dentry,
                                                    unsigned int flags,
                                                    int ret) {
    on_path_rename(ctx, old_dir, old_dentry, new_dir, new_dentry);
    return ret;
}

SEC("lsm/path_rename")
int BPF_PROG(path_rename,
             struct path *old_dir,
             struct dentry *old_dentry,
             struct path *new_dir,
             struct dentry *new_dentry) {
    if (LINUX_KERNEL_VERSION >= KERNEL_VERSION(5, 19, 0)) {
        unsigned int flags = (unsigned int) ctx[4];
        int ret = (int)(ctx[5]);
        return shim_5_19_on_path_rename(ctx, old_dir, old_dentry, new_dir,
                                        new_dentry, flags, ret);
    } else {
        on_path_rename(ctx, old_dir, old_dentry, new_dir, new_dentry);
        return (int)(ctx[4]);
    }
}
#else
SEC("kprobe/security_path_rename")
int BPF_KPROBE(security_path_rename,
               struct path *old_dir,
               struct dentry *old_dentry,
               struct path *new_dir,
               struct dentry *new_dentry) {
    on_path_rename(ctx, old_dir, old_dentry, new_dir, new_dentry);
    return 0;
}
#endif
