use pulsar_core::event::<PERSON><PERSON>;
use std::{fmt::Display, path::Path};

use crate::file_system_monitor::get_unix_username;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct ProcessInfo {
    pub pid: i32,
    pub uid: u32,
    pub exec: String,
    pub argv: String,
    pub username: String,
}

impl From<&Header> for ProcessInfo {
    fn from(value: &Header) -> Self {
        ProcessInfo {
            pid: value.pid,
            uid: value.uid,
            exec: value.image.clone(),
            username: get_unix_username(value.uid).unwrap_or_default(),
            ..Default::default()
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Default)]
pub enum Operation {
    FileCreated,
    FileDeleted,
    DirCreated,
    DirDeleted,
    FileOpened,
    FileLink,
    FileRename,
    #[default]
    Unknown,
}

impl Display for Operation {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Operation::FileCreated => write!(f, "file_create"),
            Operation::FileDeleted => write!(f, "file_deleted"),
            Operation::DirCreated => write!(f, "dir_created"),
            Operation::DirDeleted => write!(f, "dir_deleted"),
            Operation::FileOpened => write!(f, "file_opened"),
            Operation::FileLink => write!(f, "file_link"),
            Operation::FileRename => write!(f, "file_rename"),
            Operation::Unknown => write!(f, "unknown"),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct FileEvent {
    pub process: ProcessInfo,
    pub operation: Operation,
    pub source_path: String,
    pub target_path: Option<String>,
    pub timestamp: i64,
}

impl FileEvent {
    pub fn unique_key(&self) -> String {
        format!(
            "{}-{}-{}",
            self.process.pid, self.operation, self.source_path
        )
    }

    pub fn should_skip_for_path(source_path: &String) -> bool {
        let source_extension = Path::new(source_path).extension();
        let excluded_extensions = [
            "log", "tmp", "temp", "swp", "swo", "swx", "bak", "pid", "lock", "cache", "sock", "db",
            "sqlite", "ldb", "journal", "core", // zip extensions
            "gz", "tar", "zip", "bz2", "xz",
        ];

        match source_extension {
            Some(ext) => {
                if excluded_extensions.contains(&ext.to_string_lossy().to_string().as_str()) {
                    return true;
                }
            }
            None => {}
        }
        false
    }
}
