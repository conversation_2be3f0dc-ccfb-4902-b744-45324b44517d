use crate::{
    file_system_monitor::{pulsar::FileSystemMonitorModule, BlockRule},
    FileEvent, LinuxEbpfHandlerError,
};
use logger::{error, info, trace, ModuleLogger, WithSubscriber};
use pulsar::cli;
use std::{path::PathBuf, sync::Arc};
use tokio::sync::mpsc::{self, Sender};

pub struct EbpfHandler {
    channel: Sender<FileEvent>,
    paths: Arc<Vec<PathBuf>>,
    block_rules: Arc<Vec<BlockRule>>,
}

impl EbpfHandler {
    pub fn new(
        channel: Sender<FileEvent>,
        paths: Vec<PathBuf>,
        block_rules: Vec<BlockRule>,
    ) -> Self {
        Self {
            channel,
            paths: Arc::new(paths),
            block_rules: Arc::new(block_rules),
        }
    }

    pub async fn start(&self, logger: Arc<ModuleLogger>) -> Result<(), LinuxEbpfHandlerError> {
        info!("---------------------- Starting EBPF Handler ------------------------");

        let (tx, mut rx) = mpsc::channel(100);

        let options = cli::pulsard::PulsarDaemonOpts { config_file: None };

        let included_paths = self.paths.clone();
        let block_rules = self.block_rules.clone();

        let mut proxy_task = tokio::spawn(
            async move {
                // Run pulsar-exec with crate provided modules
                #[allow(clippy::blocks_in_conditions)]
                pulsar::pulsard::pulsar_daemon_run(&options, move |starter| {
                    match starter.add_module(FileSystemMonitorModule {
                        tx,
                        included_paths,
                        block_rules,
                    }) {
                        Err(error) => {
                            error!(?error, "Failed to add file system monitor module");
                        }
                        _ => {}
                    };

                    Ok(())
                })
                .await
            }
            .with_subscriber(logger.subscriber()),
        );

        let result;

        loop {
            tokio::select! {
                biased;


                response = &mut proxy_task => {
                    match response {
                        Ok(_) => {
                            info!("EBPF Proxy task has finished execution");
                            result = Ok(());
                            break;
                        }
                        Err(error) => {
                            error!(?error, "Failed to finish ebpf proxy task");
                            result = Err(error.into());
                            break;
                        }
                    }
                }

                message = rx.recv() => {
                    if let Some(message) = message {
                        trace!("Got event from ebpf {message:?}");
                        if let Err(error) = self.channel.send(message).await {
                            error!(?error, "Failed to forward event to main watcher {:?}", error.0);
                        }
                    }
                }
            }
        }

        // let result = ;

        info!("Linux EBPF Watcher finished executing");

        result
    }
}
