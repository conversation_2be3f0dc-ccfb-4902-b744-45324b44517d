use bpf_common::{
    aya::maps::Array, ebpf_program, parsing::BufferIndex, program::BpfContext, BpfSender, Program,
    ProgramBuilder, ProgramError,
};
use logger::error;
use serde::Deserialize;
use std::path::PathBuf;

const MODULE_NAME: &str = "file-system-monitor";

#[derive(Debug, Deserialize, Clone)]
pub struct BlockRule {
    /// Path prefix to block
    pub path: PathBuf,

    /// Optional user restriction (UID). If None, applies to all users.
    #[serde(default)]
    pub uid: Option<u32>,
}

#[repr(C)]
#[derive(<PERSON><PERSON>, Co<PERSON>)]
pub struct BpfBlockRule {
    pub uid: u32, // 0xFFFFFFFF = all users
    pub path: [u8; 256],
}

unsafe impl bpf_common::aya::Pod for BpfBlockRule {}

impl From<&BlockRule> for BpfBlockRule {
    fn from(r: &BlockRule) -> Self {
        let mut path = [0u8; 256];
        let p = r.path.to_string_lossy();
        let bytes = p.as_bytes();
        let len = std::cmp::min(bytes.len(), 255);
        path[..len].copy_from_slice(&bytes[..len]);

        Self {
            uid: r.uid.unwrap_or(u32::MAX),
            path,
        }
    }
}

pub async fn program(
    ctx: BpfContext,
    sender: impl BpfSender<FsEvent>,
) -> Result<Program, ProgramError> {
    let attach_to_lsm = ctx.lsm_supported();
    let binary = ebpf_program!(&ctx, "probes");
    let mut builder = ProgramBuilder::new(ctx, MODULE_NAME, binary);
    // LSM hooks provide the perfet intercept point for file system operations.
    // If LSM eBPF programs is not supported, we'll attach to the same kernel
    // functions, but using kprobes.
    logger::debug!("Attaching to lsm {}", attach_to_lsm);
    if attach_to_lsm {
        builder = builder
            .lsm("path_mknod")
            .lsm("path_unlink")
            .lsm("path_mkdir")
            .lsm("path_rmdir")
            .lsm("path_rename")
            .lsm("file_open")
            .lsm("path_link")
            .lsm("path_symlink");
    } else {
        builder = builder
            .kprobe("security_path_mknod")
            .kprobe("security_path_unlink")
            .kprobe("security_path_mkdir")
            .kprobe("security_path_rmdir")
            .kprobe("security_path_rename")
            .kprobe("security_file_open")
            .kprobe("security_path_link")
            .kprobe("security_path_symlink");
    }
    let mut program = match builder.start().await {
        Ok(program) => program,
        Err(error) => {
            error!(?error, "Failed to start file system monitor program");
            return Err(error);
        }
    };
    program.read_events("map_output_fs_event", sender).await?;
    Ok(program)
}

#[allow(dead_code)]
#[derive(Debug)]
#[repr(C)]
pub enum FsEvent {
    FileCreated {
        filename: BufferIndex<str>,
    },
    FileDeleted {
        filename: BufferIndex<str>,
    },
    DirCreated {
        filename: BufferIndex<str>,
    },
    DirDeleted {
        filename: BufferIndex<str>,
    },
    FileOpened {
        filename: BufferIndex<str>,
        flags: i32,
    },
    FileLink {
        source: BufferIndex<str>,
        destination: BufferIndex<str>,
        hard_link: bool,
    },
    FileRename {
        source: BufferIndex<str>,
        destination: BufferIndex<str>,
    },
}

pub(crate) fn get_unix_username(uid: u32) -> Option<String> {
    unsafe {
        let mut result = std::ptr::null_mut();
        let amt = match libc::sysconf(libc::_SC_GETPW_R_SIZE_MAX) {
            n if n < 0 => 512 as usize,
            n => n as usize,
        };
        let mut buf = Vec::with_capacity(amt);
        let mut passwd: libc::passwd = std::mem::zeroed();

        match libc::getpwuid_r(
            uid,
            &mut passwd,
            buf.as_mut_ptr(),
            buf.capacity() as libc::size_t,
            &mut result,
        ) {
            0 if !result.is_null() => {
                let ptr = passwd.pw_name as *const _;
                let username = std::ffi::CStr::from_ptr(ptr).to_str().unwrap().to_owned();
                Some(username)
            }
            _ => None,
        }
    }
}

pub mod pulsar {

    use crate::{FileEvent, Operation};
    use std::{path::Path, sync::Arc, time::SystemTime};

    use super::*;
    use bpf_common::{parsing::IndexError, program::BpfEvent, Pid};
    use chrono::{DateTime, Utc};
    use logger::error;
    use pulsar_core::{
        event::FileFlags,
        pdk::{
            Event, IntoPayload, ModuleContext, ModuleError, NoConfig, Payload, SimplePulsarModule,
        },
        Timestamp,
    };
    use tokio::sync::mpsc::Sender;

    pub struct FileSystemMonitorModule {
        pub tx: Sender<FileEvent>,
        pub included_paths: Arc<Vec<PathBuf>>,
        pub block_rules: Arc<Vec<BlockRule>>,
    }

    impl SimplePulsarModule for FileSystemMonitorModule {
        type Config = NoConfig;
        type State = FileSystemMonitorState;

        const MODULE_NAME: &'static str = MODULE_NAME;
        const DEFAULT_ENABLED: bool = true;

        async fn init_state(
            &self,
            _config: &Self::Config,
            ctx: &ModuleContext,
        ) -> Result<Self::State, ModuleError> {
            let mut bpf = match program(ctx.get_bpf_context(), ctx.clone()).await {
                Ok(bpf) => bpf,
                Err(error) => {
                    error!(?error, "BPF Program error failed to start program");
                    return Err(error.into());
                }
            };
            let mut rules =
                Array::<_, BpfBlockRule>::try_from(bpf.bpf().map_mut("block_rules").unwrap())?;

            for (i, rule) in self.block_rules.iter().enumerate() {
                if i >= 128 {
                    break;
                }
                rules.set(i as u32, BpfBlockRule::from(rule), 0)?;
            }

            Ok(Self::State {
                _ebpf_program: bpf,
                channel: self.tx.clone(),
                included_paths: self.included_paths.clone(),
            })
        }

        async fn on_event(
            event: &Event,
            _config: &Self::Config,
            state: &mut Self::State,
            ctx: &ModuleContext,
        ) -> Result<(), ModuleError> {
            let mut should_consider_event = false;
            let mut file_event = FileEvent::default();
            match event.payload() {
                Payload::FileCreated { filename } => {
                    if state.should_consider_event(filename) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileCreated;
                        file_event.source_path = filename.clone();
                    }
                }
                Payload::FileDeleted { filename } => {
                    if state.should_consider_event(filename) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileDeleted;
                        file_event.source_path = filename.clone();
                    }
                }
                Payload::DirCreated { dirname } => {
                    if state.should_consider_event(dirname) {
                        should_consider_event = true;
                        file_event.operation = Operation::DirCreated;
                        file_event.source_path = dirname.clone();
                    }
                }
                Payload::DirDeleted { dirname } => {
                    if state.should_consider_event(dirname) {
                        should_consider_event = true;
                        file_event.operation = Operation::DirDeleted;
                        file_event.source_path = dirname.clone();
                    }
                }
                Payload::FileOpened { filename, flags: _ } => {
                    if state.should_consider_event(filename) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileOpened;
                        file_event.source_path = filename.clone();
                    }
                }
                Payload::FileRename {
                    source,
                    destination,
                } => {
                    if state.should_consider_event(source) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileRename;
                        file_event.source_path = source.clone();
                        file_event.target_path = Some(destination.clone());
                    }
                }
                _ => {}
            }

            if should_consider_event {
                file_event.process = event.header().into();
                let system_time: SystemTime = event.header().timestamp.into();
                let timestamp: DateTime<Utc> = system_time.into();
                file_event.timestamp = timestamp.timestamp();

                match ctx
                    .get_process_tracker()
                    .get(Pid::from_raw(event.header().pid), Timestamp::now())
                    .await
                {
                    Ok(process) => {
                        file_event.process.argv = process.argv.join(" ");
                    }
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to get process information for event {:?}",
                            event.payload()
                        );
                    }
                }
                if let Err(error) = state.channel.send(file_event).await {
                    error!(?error, "Failed to send file_event {:?}", error.0);
                }
            }

            Ok(())
        }
    }

    pub struct FileSystemMonitorState {
        _ebpf_program: Program,
        included_paths: Arc<Vec<PathBuf>>,
        channel: Sender<FileEvent>,
    }

    impl FileSystemMonitorState {
        fn should_consider_event(&self, path: &String) -> bool {
            let event_path = Path::new(path);
            self.included_paths
                .iter()
                .filter(|p| event_path.starts_with(p))
                .count()
                > 0
                && FileEvent::should_skip_for_path(path) == false
        }
    }

    impl IntoPayload for FsEvent {
        type Error = IndexError;

        fn try_into_payload(data: BpfEvent<Self>) -> Result<Payload, Self::Error> {
            let BpfEvent {
                payload, buffer, ..
            } = data;
            Ok(match payload {
                FsEvent::FileCreated { filename } => Payload::FileCreated {
                    filename: filename.string(&buffer)?,
                },
                FsEvent::FileDeleted { filename } => Payload::FileDeleted {
                    filename: filename.string(&buffer)?,
                },
                FsEvent::DirCreated { filename } => Payload::DirCreated {
                    dirname: filename.string(&buffer)?,
                },
                FsEvent::DirDeleted { filename } => Payload::DirDeleted {
                    dirname: filename.string(&buffer)?,
                },
                FsEvent::FileOpened { filename, flags } => Payload::FileOpened {
                    filename: filename.string(&buffer)?,
                    flags: FileFlags::from_raw_unchecked(flags),
                },
                FsEvent::FileLink {
                    source,
                    destination,
                    hard_link,
                } => Payload::FileLink {
                    source: source.string(&buffer)?,
                    destination: destination.string(&buffer)?,
                    hard_link,
                },
                FsEvent::FileRename {
                    source,
                    destination,
                } => Payload::FileRename {
                    source: source.string(&buffer)?,
                    destination: destination.string(&buffer)?,
                },
            })
        }
    }
}
