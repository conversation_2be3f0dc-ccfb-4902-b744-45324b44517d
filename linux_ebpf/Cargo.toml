[package]
name = "linux_ebpf"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
tokio = { version = "1.46.1" }
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
libc = "0.2.174"
chrono = "0.4.41"
serde = { version = "1.0.219", features = ["derive"] }

[target.'cfg(target_os = "linux")'.dependencies]
pulsar = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45", default-features = false, features = [
  "sqlite3-vendored",
  "process-monitor",
] }
bpf-common = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45" }
pulsar-core = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45" }

[target.'cfg(target_os = "linux")'.build-dependencies]
bpf-builder = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45" }
