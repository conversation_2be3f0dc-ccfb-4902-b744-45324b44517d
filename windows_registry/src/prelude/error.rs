use anyhow::Error as AnyhowError;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum WinRegistryError {
    #[error("Win Registry Error: Failed to open registry sub key {0}")]
    UnableToOpenSubKey(String),

    #[error("Win Registry Error: Invalid path {0} path must contains atleast 2 segments")]
    InvalidRegistryPath(String),

    #[error("Win Registry Error: Hive is unknown {0}")]
    UnknownHiveError(String),

    #[error("Win Registry Error: Unknown error")]
    UnknownError(#[from] AnyhowError),
}
