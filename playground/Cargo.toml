[package]
name = "playground"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
ipc = { path = "../ipc" }
database = { path = "../database" }
fim = { path = "../fim" }
data_collection = { path = "../data_collection" }
logger = { path = "../logger" }
agents = { path = "../agents" }
utils = { path = "../utils" }
tokio = { version = "1.46.1", features = ["full", "tracing"] }
encoding_rs_io = "0.1.7"
encoding_rs = "0.8.35"
serde_json = "1.0.140"
glob = "0.3.2"
rayon = "1.10.0"
walkdir = "2.5.0"
ignore = "0.4.23"
globset = "0.4.16"
sysinfo = "0.36.0"
shell = { path = "../shell" }

[target.'cfg(windows)'.dependencies]
windows_patch_xml_checker = { path = "../windows_patch_xml_checker" }
[target.'cfg(target_os = "linux")'.dependencies]
linux_ebpf = { path = "../linux_ebpf" }
