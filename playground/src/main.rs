use std::path::{Path, PathBuf};

use data_collection::{
    Certificates, DataCollectionExtension, NetworkInterfaces, OsServices, Processes, Provision,
    Resources, Softwares, StartUpItems, Users,
};
use database::{
    models::{AgentConfig, AgentMetadata},
    Database, DbOptions,
};
use linux_ebpf::BlockRule;
use logger::{LogLevelManager, ModuleLogger, LOG_LEVEL_MANAGER};
use tokio::{fs, sync::mpsc};
use utils::dir::{get_db_dir, get_log_dir};

#[tokio::main]
async fn main() {
    LOG_LEVEL_MANAGER.get_or_init(|| {
        let manager = LogLevelManager::new();
        manager
            .set_level("debug")
            .expect("Failed to set Global Log Level");
        manager
    });

    // Database::init(DbOptions::new(
    //     get_db_dir(),
    //     "endpointops",
    //     "endpointops",
    //     "endpointops",
    //     "endpointops_ziro@ziro@2019",
    // ))
    // .await
    // .unwrap();
    // console_subscriber::init();
    let module_loger = ModuleLogger::new("global", None, Some("endpointops".to_owned()));

    // console_subscriber::init();

    let _guard = module_loger.guard();

    module_loger.set_global().unwrap();

    let (tx, mut rx) = mpsc::channel(10);

    let handler = linux_ebpf::EbpfHandler::new(
        tx,
        vec![Path::new("/home/<USER>/fim_test").to_path_buf()],
        vec![BlockRule {
            path: Path::new("/home/<USER>/fim_block_test").to_path_buf(),
            uid: None,
        }],
    );

    tokio::join!(
        tokio::spawn(async move {
            handler.start(module_loger.clone()).await.ok();
        }),
        tokio::spawn(async move {
            while let Some(x) = rx.recv().await {
                logger::info!("Received event {x:?}");
            }
        })
    );

    // let agent_metadata = AgentMetadata::new(6, AgentConfig::default());

    // let mut provision_data = Provision::new("uuid".to_owned(), "enroll_secret".to_owned());
    // provision_data.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("provision.json"),
    //     serde_json::to_string_pretty(&provision_data.get_data()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut resources = Resources::new(&agent_metadata);
    // resources.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("resources.json"),
    //     serde_json::to_string_pretty(&resources.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut processes = Processes::new(&agent_metadata);
    // processes.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("processes.json"),
    //     serde_json::to_string_pretty(&processes.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut users = Users::new(&agent_metadata);
    // users.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("users.json"),
    //     serde_json::to_string_pretty(&users.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut startup = StartUpItems::new(&agent_metadata);
    // startup.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("startup.json"),
    //     serde_json::to_string_pretty(&startup.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut network_interfaces = NetworkInterfaces::new(&agent_metadata);
    // network_interfaces.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("network_interfaces.json"),
    //     serde_json::to_string_pretty(&network_interfaces.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut certificates = Certificates::new(&agent_metadata);
    // certificates.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("certificates.json"),
    //     serde_json::to_string_pretty(&certificates.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut softwares = Softwares::new(&agent_metadata);
    // softwares.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("softwares.json"),
    //     serde_json::to_string_pretty(&softwares.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();

    // let mut services = OsServices::new(&agent_metadata);
    // services.collect().await.unwrap();
    // fs::write(
    //     get_log_dir().join("os_services.json"),
    //     serde_json::to_string_pretty(&services.build_payload().unwrap()).unwrap(),
    // )
    // .await
    // .unwrap();
}
