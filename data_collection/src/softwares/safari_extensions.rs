use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use glob::glob;
use logger::{error, trace};
use plist::Value;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use serde_json::json;
use std::{collections::HashSet, path::Path};
use utils::shutdown::is_system_running;

#[derive(Debug, Serialize, Hash, PartialEq, Eq, Default)]
pub struct SafariExtension {
    name: String,
    bundle_id: String,
    version: String,
    sdk: String,
    description: String,
    path: String,
    bundle_version: String,
    copyright: String,
}

impl From<SafariExtension> for Software {
    fn from(value: SafariExtension) -> Self {
        Software {
            name: value.name,
            version: value.version,
            r#type: PackageType::SafariBrowserPlugin,
            bundle_identifier: value.bundle_id,
            properties: json!({
                "sdk": value.sdk,
                "description": value.description,
                "path": value.path,
                "bundle_version": value.bundle_version,
                "copyright": value.copyright
            }),
            ..Default::default()
        }
    }
}

impl SafariExtension {
    fn generate_extension(plist_path: &Path) -> Option<SafariExtension> {
        trace!("Scanning file {}", plist_path.display());
        let plist_parsed = match Value::from_file(plist_path) {
            Ok(value) => value,
            Err(error) => {
                error!(?error, "Failed to parse plist {}", plist_path.display());
                return None;
            }
        };

        let dict = plist_parsed.as_dictionary();
        if dict.is_none() {
            error!(
                "Unable to parse plist as dictionary {}",
                plist_path.display()
            );
            return None;
        }
        let dict = dict.unwrap();
        if dict.get("NSExtension").is_none() {
            error!("No NSExtension key found in dictionary");
            return None;
        }

        if let Some(ns_extension) = dict.get("NSExtension").unwrap().as_dictionary() {
            if let Some(point_id) = ns_extension.get("NSExtensionPointIdentifier") {
                if let Some(v) = point_id.as_string() {
                    if v.contains("com.apple.Safari") == false {
                        // this is not safari sandbox extension
                        return None;
                    }
                }
            }
        }
        let mut package = SafariExtension::default();
        if let Some(value) = dict.get("CFBundleIdentifier") {
            package.bundle_id = value.as_string().unwrap_or_else(|| "").to_string();
        }
        if let Some(value) = dict.get("CFBundleDisplayName") {
            package.name = value.as_string().unwrap_or_else(|| "").to_string();
        }
        if let Some(value) = dict.get("CFBundleInfoDictionaryVersion") {
            package.sdk = value.as_string().unwrap_or_else(|| "").to_string();
        }
        if let Some(value) = dict.get("CFBundleVersion") {
            package.bundle_version = value.as_string().unwrap_or_else(|| "").to_string();
        }
        if let Some(value) = dict.get("CFBundleShortVersionString") {
            package.version = value.as_string().unwrap_or_else(|| "").to_string();
        }
        if let Some(value) = dict.get("NSHumanReadableCopyright") {
            package.copyright = value.as_string().unwrap_or_else(|| "").to_string();
        }
        if let Some(value) = dict.get("NSHumanReadableDescription") {
            package.description = value.as_string().unwrap_or_else(|| "").to_string();
        }

        Some(package)
    }

    fn process_app_directory(path: &str) -> HashSet<SafariExtension> {
        let glob_pattern = format!("{}/Contents/PlugIns/*.appex/Contents/Info.plist", path);
        match glob(&glob_pattern) {
            Ok(paths) => paths
                .into_iter()
                .filter_map(Result::ok)
                .map(|item| SafariExtension::generate_extension(item.as_path()))
                .filter(|item| item.is_some())
                .map(|item| {
                    let mut ext = item.unwrap();
                    ext.path = path.to_owned();
                    ext
                })
                .collect::<HashSet<SafariExtension>>(),
            Err(error) => {
                error!(?error, "Failed to process glob pattern {}", glob_pattern);
                return HashSet::new();
            }
        }
    }

    fn collect_site(path: &str) -> HashSet<SafariExtension> {
        let paths = match glob(&path) {
            Ok(paths) => paths
                .into_iter()
                .filter_map(Result::ok)
                .map(|e| e.to_string_lossy().to_string())
                .collect::<Vec<String>>(),
            Err(error) => {
                error!(?error, "Failed to read glob pattern");
                return HashSet::new();
            }
        };
        paths
            .into_iter()
            .flat_map(|p| SafariExtension::process_app_directory(&p))
            .collect()
    }

    pub fn collect() -> HashSet<Software> {
        execute_in_thread_pool(|| {
            [
                "/Applications/*",
                "/Users/<USER>/Applications/*",
                "/Library/Safari/Extensions/*",
                "/Users/<USER>/Library/Safari/Extensions/*",
                "/Users/<USER>/Library/Containers/com.apple.Safari/Data/Library/Safari/AppExtensions/*",
            ]
            .into_par_iter()
            .take_any_while(|_| is_system_running())
            .flat_map_iter(|i| SafariExtension::collect_site(i))
            .map(|i| i.into())
            .collect()
        })
    }
}
