use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use glob::glob;
use logger::{error, trace};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde_json::json;
use std::{
    collections::HashSet,
    fs::{self, File},
    io::{BufRead, BufReader},
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;

#[derive(Debug, PartialEq, Eq, Hash, Default)]
pub struct PythonPackage {
    name: String,
    version: String,
    summary: String,
    author: String,
    license: String,
    path: String,
    directory: String,
}

impl From<PythonPackage> for Software {
    fn from(value: PythonPackage) -> Self {
        Software {
            name: value.name,
            version: value.version,
            r#type: PackageType::PythonPackages,
            vendor: value.author,
            properties: json!({
                "summary": value.summary,
                "path": value.path,
                "license": value.license,
                "directory": value.directory,
            }),
            ..Default::default()
        }
    }
}

impl PythonPackage {
    fn collect_package(path: &Path) -> Option<PythonPackage> {
        trace!("Collecting package from {}", path.display());

        let file = if path.to_string_lossy().ends_with(".dist-info") {
            path.join("METADATA")
        } else {
            path.join("PKG-INFO")
        };

        let reader = match File::open(&file) {
            Ok(entry) => BufReader::new(entry),
            Err(error) => {
                error!(?error, "Failed to read file {}", file.display());
                return None;
            }
        };

        let parts = reader
            .lines()
            .into_iter()
            .filter_map(Result::ok)
            .filter(|data| data.split(":").count() == 2)
            .map(|line| {
                let parts = line.split(":").collect::<Vec<&str>>();
                (
                    parts.first().unwrap().trim().to_string(),
                    parts.last().unwrap().trim().to_string(),
                )
            })
            .collect::<Vec<(String, String)>>();

        let mut package = PythonPackage::default();
        package.path = path.to_string_lossy().to_string();

        for (key, value) in parts {
            if key.to_lowercase() == "name" {
                package.name = value;
            } else if key.to_lowercase() == "version" {
                package.version = value;
            } else if key.to_lowercase() == "summary" {
                package.summary = value;
            } else if key.to_lowercase() == "author" {
                package.author = value;
            } else if key.to_lowercase() == "license" {
                package.license = value;
            }
        }

        Some(package)
    }

    fn collect_from_directory(path: &Path) -> HashSet<PythonPackage> {
        trace!("Visiting directory {}", path.display());
        let directory_entries = match fs::read_dir(path) {
            Ok(dir) => dir,
            Err(error) => {
                error!(?error, "Error Listing directory {}", path.display());
                return HashSet::new();
            }
        };

        directory_entries
            .into_iter()
            .filter(|item| {
                item.as_ref().is_ok_and(|entry| {
                    entry.path().is_dir()
                        && (entry.path().to_string_lossy().ends_with(".dist-info")
                            || entry.path().to_string_lossy().ends_with(".egg-info"))
                })
            })
            .map(|entry| entry.unwrap().path())
            .map(|dir| PythonPackage::collect_package(dir.as_path()))
            .filter(|package| package.is_some())
            .map(|item| {
                let mut package = item.unwrap();
                package.directory = path.to_string_lossy().to_string();
                package
            })
            .collect()
    }

    fn collect_from_site(path: &Path) -> HashSet<PythonPackage> {
        trace!("Collecting package from site {}", path.display());
        match glob(path.to_string_lossy().as_ref()) {
            Ok(paths) => paths
                .into_iter()
                .filter(|p| {
                    p.as_ref()
                        .is_ok_and(|item| !item.is_symlink() && item.is_dir())
                })
                .map(|p| p.unwrap())
                .flat_map(|path| PythonPackage::collect_from_directory(path.as_path()))
                .collect(),
            Err(error) => {
                error!(?error, "Failed to read glob pattern {}", path.display());
                HashSet::new()
            }
        }
    }

    fn traverse_version(paths: Vec<String>) -> Vec<String> {
        paths
            .into_iter()
            .map(|path| glob(&path))
            .filter_map(Result::ok)
            .flat_map(|dir| {
                dir.into_iter()
                    .filter(|item| item.as_ref().is_ok_and(|entry| !entry.is_symlink()))
                    .flat_map(|path| {
                        [
                            "lib/python*/site-packages".to_owned(),
                            "lib/site-packages".to_owned(),
                        ]
                        .into_iter()
                        .map(move |p| path.as_ref().unwrap().join(p))
                    })
            })
            .map(|p| p.to_string_lossy().to_string())
            .collect::<Vec<String>>()
    }

    #[cfg(target_os = "macos")]
    fn mac_os_system_paths() -> Vec<String> {
        let mac_paths = PythonPackage::traverse_version(vec![
            "/System/Library/Frameworks/Python.framework/Versions".to_owned(),
            "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions"
                .to_owned(),
        ]);

        trace!("MAC Paths: {:?}", mac_paths);

        mac_paths
    }

    fn user_paths() -> Vec<String> {
        let user_paths = {
            #[cfg(target_os = "linux")]
            {
                vec![
                    "/home/<USER>/.pyenv/versions".to_owned(),
                    "/home/<USER>/.local/lib/python*".to_owned(),
                ]
            }
            #[cfg(target_os = "macos")]
            {
                vec![
                    "/Users/<USER>/.pyenv/versions".to_owned(),
                    "/Users/<USER>/.local/lib/python".to_owned(),
                    "/Users/<USER>/Library/Python".to_owned(),
                ]
            }
            #[cfg(target_os = "windows")]
            {
                use windows_registry::WinRegistry;

                WinRegistry::get_users_key()
                    .into_iter()
                    .flat_map(|user| {
                        PythonPackage::list_win_reg_paths(&format!(
                            "HKU\\{}\\SOFTWARE\\Python\\PythonCore",
                            user
                        ))
                    })
                    .chain(
                        [
                            "C:\\Users\\<USER>\\.pyenv\\versions".to_owned(),
                            "C:\\Users\\<USER>\\.local\\lib\\python".to_owned(),
                            "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python*".to_owned(),
                        ]
                        .into_iter(),
                    )
                    .collect::<Vec<String>>()
            }
        };
        PythonPackage::traverse_version(user_paths)
    }

    #[cfg(windows)]
    fn list_win_reg_paths(prefix: &str) -> Vec<String> {
        use windows_registry::WinRegistry;

        let paths = PathBuf::from(prefix);
        let paths: Vec<String> = match WinRegistry::new(&paths) {
            Ok(reg) => reg
                .keys()
                .into_iter()
                .map(|version_key| {
                    match WinRegistry::new(paths.join(version_key).join("InstallPath")) {
                        Ok(path) => path.get_value::<String>("".to_owned()),
                        Err(_) => "".to_owned(),
                    }
                })
                .filter(|item| !item.is_empty())
                .collect(),
            Err(_) => {
                return vec![];
            }
        };
        PythonPackage::traverse_version(paths)
    }

    pub fn collect() -> HashSet<Software> {
        let mut python_paths: Vec<String> = {
            #[cfg(windows)]
            {
                PythonPackage::list_win_reg_paths("HKLM\\SOFTWARE\\Python\\PythonCore")
            }
            #[cfg(not(windows))]
            {
                vec![
                    "/usr/local/lib/python*/dist-packages".to_owned(),
                    "/usr/local/lib/python*/site-packages".to_owned(),
                    "/usr/local/lib64/python*/site-packages".to_owned(),
                    "/opt/homebrew/lib/python*/dist-packages".to_owned(),
                    "/opt/homebrew/lib/python*/site-packages".to_owned(),
                    "/usr/lib/python*/dist-packages".to_owned(),
                    "/usr/lib/python*/site-packages".to_owned(),
                    "/Library/Python/*/site-packages".to_owned(),
                ]
            }
        };

        #[cfg(target_os = "macos")]
        {
            python_paths.extend(PythonPackage::mac_os_system_paths());
        }

        python_paths.extend(PythonPackage::user_paths());

        execute_in_thread_pool(|| {
            python_paths
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .flat_map_iter(|prefix| {
                    PythonPackage::collect_from_site(PathBuf::from(prefix).as_path())
                })
                .map(|package| package.into())
                .collect()
        })
    }
}
