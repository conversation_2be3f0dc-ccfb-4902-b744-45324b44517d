use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use logger::{error, trace};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde_json::json;
use std::{
    collections::HashSet,
    fs,
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;

#[derive(Debug, PartialEq, Eq, Hash, Default)]
pub struct HomebrewPackage {
    name: String,
    version: String,
    path: String,
    formula_type: String,
    prefix: String,
}

impl From<HomebrewPackage> for Software {
    fn from(value: HomebrewPackage) -> Self {
        Software {
            name: value.name,
            version: value.version,
            r#type: PackageType::HomebrewPackages,
            properties: json!({
                "formula_type": value.formula_type,
                "path": value.path,
                "prefix": value.prefix
            }),
            ..Default::default()
        }
    }
}

impl HomebrewPackage {
    fn collect_package(path: &Path) -> Option<HomebrewPackage> {
        trace!("Collecting package from {}", path.display());

        let entry = match fs::read_dir(path) {
            Ok(entry) => entry,
            Err(error) => {
                error!(?error, "Failed to read directory {}", path.display());
                return None;
            }
        };

        let mut package = HomebrewPackage::default();

        package.version = entry
            .into_iter()
            .filter_map(Result::ok)
            .fold("".to_string(), |_, entry| {
                entry.file_name().to_string_lossy().to_string()
            });

        package.name = path
            .file_name()
            .map_or("".to_owned(), |v| v.to_string_lossy().to_string());

        package.path = path.to_string_lossy().to_string();

        Some(package)
    }

    fn collect_from_prefix_directory(package_type: &str, path: &Path) -> HashSet<HomebrewPackage> {
        trace!(
            "Collecting package from prefix directory {}",
            path.display()
        );
        let directory_entries = match fs::read_dir(path) {
            Ok(dir) => dir,
            Err(error) => {
                error!(?error, "Error Listing directory {}", path.display());
                return HashSet::new();
            }
        };

        directory_entries
            .into_iter()
            .filter_map(Result::ok)
            .map(|entry| entry.path())
            .map(|dir| HomebrewPackage::collect_package(dir.as_path()))
            .filter(|package| package.is_some())
            .map(|item| {
                let mut package = item.unwrap();
                package.formula_type = package_type.to_owned();
                package.prefix = path.to_string_lossy().to_string();
                package
            })
            .collect()
    }

    fn collect_from_prefix(prefix: &str) -> HashSet<HomebrewPackage> {
        let types = [
            ("formula", PathBuf::from(prefix).join("Cellar")),
            ("cask", PathBuf::from(prefix).join("Caskroom")),
        ];

        types
            .into_iter()
            .filter(|item| item.1.exists())
            .flat_map(|item| {
                HomebrewPackage::collect_from_prefix_directory(item.0, item.1.as_path())
            })
            .collect()
    }

    pub fn collect() -> HashSet<Software> {
        execute_in_thread_pool(|| {
            ["/usr/local", "/opt/homebrew"]
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .flat_map_iter(|prefix| HomebrewPackage::collect_from_prefix(prefix))
                .map(|package| package.into())
                .collect()
        })
    }
}
