#[cfg(windows)]
mod chocolatey_packages;
mod chrome_extensions;
#[cfg(target_os = "linux")]
mod deb_packages;
#[cfg(target_os = "linux")]
mod rpm_packages;
mod firefox_extensions;
#[cfg(not(windows))]
mod homebrew_packages;
#[cfg(windows)]
mod ie_extensions;
#[cfg(target_os = "macos")]
mod macos_apps;
mod npm_packages;
mod python_packages;
#[cfg(target_os = "macos")]
mod safari_extensions;
mod software;
mod softwares;
#[cfg(windows)]
mod windows_program;

pub use softwares::*;
