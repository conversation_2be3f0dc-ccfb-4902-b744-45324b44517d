use crate::DataCollectionError;
use anyhow::Result;
use api::data_collection::send_system_data;
use async_trait::async_trait;
use logger::{error, ModuleLogger};
use serde_json::Value;
use std::fmt::Debug;
use std::sync::Arc;
use utils::dir::get_log_dir;

pub enum ExtensionType {
    Users,
    StartUpItems,
    Softwares,
    Resources,
    RemoteConnections,
    Processes,
    NetworkInterfaces,
    ListeningPorts,
    Certificates,
    Provision,
}

#[async_trait]
pub trait DataCollectionExtension
where
    Self: Debug + Send + Sync,
{
    fn get_name(&self) -> &str;

    fn get_refresh_interval(&self) -> u64;

    fn get_endpoint_id(&self) -> i64;

    fn build_payload(&self) -> Result<Value, DataCollectionError>;

    async fn collect(&mut self, logger: Arc<ModuleLogger>) -> Result<(), DataCollectionError>;

    fn logger(&self) -> Arc<ModuleLogger> {
        ModuleLogger::new(
            self.get_name().to_lowercase().as_str(),
            Some(get_log_dir().join("extensions")),
            Some(self.get_name().to_lowercase()),
        )
    }

    async fn send(&mut self) -> Result<(), DataCollectionError> {
        Ok(send_system_data(self.build_payload()?).await?)
    }

    async fn collect_and_send(
        &mut self,
        logger: Option<Arc<ModuleLogger>>,
    ) -> Result<(), DataCollectionError> {
        match self.collect(logger.unwrap_or_else(|| self.logger())).await {
            Ok(_) => match self.send().await {
                Err(error) => {
                    error!(?error, "Failed to send data to server for {:?}", self);
                    return Err(error);
                }
                _ => return Ok(()),
            },
            Err(error) => {
                error!(?error, "Failed to collect data for {:?}", self);
                return Err(error);
            }
        }
    }
}
