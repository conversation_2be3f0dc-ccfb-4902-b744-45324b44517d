use logger::trace;
use serde::Serialize;
use sysinfo::{CpuRefreshKind, RefreshKind, System};

#[derive(Debug, Serialize, Default)]
pub struct Cpu {
    utilization: f64,
}

impl Cpu {
    pub fn collect() -> Self {
        let mut sys = System::new_with_specifics(
            RefreshKind::nothing().with_cpu(CpuRefreshKind::everything()),
        );
        // Wait a bit because CPU usage is based on diff.
        std::thread::sleep(sysinfo::MINIMUM_CPU_UPDATE_INTERVAL);
        // Refresh CPUs again to get actual value.
        sys.refresh_cpu_usage();

        let mut data = Cpu::default();
        data.utilization = f64::trunc(sys.global_cpu_usage() as f64 * 100.0) / 100.0;
        trace!("Collected CPU as {:?}", data);
        data
    }
}
