use logger::trace;
use serde::Serialize;
use sysinfo::{MemoryRefreshKind, RefreshKind, System};

#[derive(Debug, Serialize, Default)]
pub struct Memory {
    utilization: f32,
    free: u64,
    used: u64,
}

impl Memory {
    pub fn collect() -> Self {
        let sys = System::new_with_specifics(
            RefreshKind::nothing().with_memory(MemoryRefreshKind::everything()),
        );

        let mut data = Memory::default();

        data.utilization =
            f32::trunc(((sys.used_memory() * 100) / sys.total_memory()) as f32 * 100.0) / 100.0;
        data.used = sys.used_memory();
        data.free = sys.free_memory();
        trace!("Collected Memory as {:?}", data);
        data
    }
}
