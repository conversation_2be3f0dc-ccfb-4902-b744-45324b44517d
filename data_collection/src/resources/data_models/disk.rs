use logger::trace;
use serde::Serialize;
use std::collections::HashSet;
use sysinfo::{DiskRefreshKind, Disks};

#[derive(Debug, Serialize, Default, <PERSON>ialEq, Eq, Hash)]
pub struct Disk {
    pub free: u64,
    pub total: u64,
    pub used: u64,
    pub path: String,
}

impl Disk {
    pub fn collect() -> HashSet<Self> {
        let disks =
            Disks::new_with_refreshed_list_specifics(DiskRefreshKind::nothing().with_storage());
        let mut available_disks = HashSet::new();
        for disk in disks.into_iter() {
            available_disks.insert(Disk {
                free: disk.available_space(),
                total: disk.total_space(),
                used: disk.total_space() - disk.available_space(),
                path: disk.mount_point().to_string_lossy().to_string(),
            });
        }
        trace!(
            "Collected total {} Disks as {:?}",
            available_disks.len(),
            available_disks
        );
        available_disks
    }
}
