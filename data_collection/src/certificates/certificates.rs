use super::certificate::Certificate;
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::{debug, ModuleLogger};
use serde_json::{json, Value};
use std::{collections::HashSet, sync::Arc, time::Instant};

#[derive(Debug)]
pub struct Certificates<'a> {
    certificates: HashSet<Certificate>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> Certificates<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            certificates: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for Certificates<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .certificate_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "certificates_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                    "certificate_details": self.certificates
                })
        }))
    }

    async fn collect(&mut self, logger: Arc<ModuleLogger>) -> Result<(), DataCollectionError> {
        self.certificates = tokio::task::spawn_blocking(move || {
            logger.with(|| {
                let time = Instant::now();
                let collection = Certificate::collect();
                debug!("Time taken for collection {:?}", time.elapsed());
                collection
            })
        })
        .await?;
        Ok(())
    }
}
