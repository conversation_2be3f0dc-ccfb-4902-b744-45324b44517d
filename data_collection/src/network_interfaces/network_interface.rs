use crate::execute_in_thread_pool;
use logger::debug;
use netdev::{ipnet::IpNet, Interface};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use std::{collections::HashSet, net::IpAddr};
use utils::shutdown::is_system_running;

#[derive(Debug, Serialize, PartialEq, Eq, Hash, Default)]
pub struct NetworkInterface {
    interface: u32,
    address: Option<IpAddr>,
    mac: String,
    mask: Option<IpAddr>,
    broadcast: String,
    r#type: String,
    gateway: String,
    friendly_name: String,
    dhcp_enabled: String,
    // in_bytes: u64,
    // out_bytes: u64,
    status: String,
}

struct InterfaceContainer(Interface);

impl Into<HashSet<NetworkInterface>> for InterfaceContainer {
    fn into(self) -> HashSet<NetworkInterface> {
        let interface = self.0;
        interface
            .ipv4
            .iter()
            .map(|i| i.to_owned().into())
            .chain(interface.ipv6.iter().map(|item| item.to_owned().into()))
            .map(|address: IpNet| NetworkInterface {
                interface: interface.index,
                // in_bytes: interface.receive_speed.unwrap_or_default(),
                // out_bytes: interface.transmit_speed.unwrap_or_default(),
                address: Some(address.addr().into()),
                mac: interface.mac_addr.unwrap_or_default().to_string(),
                mask: Some(address.netmask().into()),
                broadcast: if interface.is_broadcast() {
                    address.broadcast().to_string()
                } else {
                    "".to_owned()
                },
                r#type: interface.if_type.name(),
                gateway: interface.gateway.as_ref().map_or("".to_owned(), |ad| {
                    ad.ipv4.first().map_or("".to_owned(), |f| f.to_string())
                }),
                friendly_name: interface
                    .friendly_name
                    .as_ref()
                    .map_or(interface.name.to_owned(), |v| v.to_owned()),
                dhcp_enabled: "no".to_owned(),
                status: if interface.is_up() { "yes" } else { "no" }.to_owned(),
            })
            .collect()
    }
}

impl NetworkInterface {
    pub fn collect() -> HashSet<Self> {
        let interfaces = execute_in_thread_pool(|| {
            netdev::get_interfaces()
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .map(|interface| InterfaceContainer(interface))
                .flat_map(|item| Into::<HashSet<NetworkInterface>>::into(item))
                .collect::<HashSet<NetworkInterface>>()
        });

        debug!(
            "Collected total {} network interfaces with unique address",
            interfaces.len(),
        );

        interfaces
    }
}
