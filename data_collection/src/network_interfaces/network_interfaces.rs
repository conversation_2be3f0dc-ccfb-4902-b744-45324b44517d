use super::network_interface::NetworkInterface;
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::{debug, ModuleLogger};
use serde_json::{json, Value};
use std::{collections::HashSet, sync::Arc, time::Instant};

#[derive(Debug)]
pub struct NetworkInterfaces<'a> {
    interfaces: HashSet<NetworkInterface>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> NetworkInterfaces<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            interfaces: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for NetworkInterfaces<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .network_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "network_interfaces_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                "interface_details": self.interfaces,
            })
        }))
    }

    async fn collect(&mut self, logger: Arc<ModuleLogger>) -> Result<(), DataCollectionError> {
        self.interfaces = tokio::task::spawn_blocking(move || {
            logger.with(|| {
                let time = Instant::now();
                let collection = NetworkInterface::collect();
                debug!("Time taken for collection {:?}", time.elapsed());
                collection
            })
        })
        .await?;
        Ok(())
    }
}
