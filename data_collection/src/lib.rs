#[macro_use]
extern crate cfg_if;

mod certificates;
mod data_collection_extension;
mod network_interfaces;
mod os_services;
mod platform;
mod prelude;
mod processes;
mod provision;
mod resources;
mod softwares;
mod start_up_items;
mod users;

pub use certificates::*;
pub use data_collection_extension::*;
use logger::error;
pub use network_interfaces::*;
pub use os_services::*;
pub use prelude::*;
pub use processes::*;
pub use provision::*;
pub use resources::*;
pub use softwares::*;
pub use start_up_items::*;
pub use users::*;
use utils::cpu::concurrency_cores;

pub(crate) fn execute_in_thread_pool<OP, R>(op: OP) -> R
where
    OP: FnOnce() -> R + Send,
    R: Send,
{
    let num_threads = concurrency_cores(25);
    match rayon::ThreadPoolBuilder::new()
        .num_threads(num_threads)
        .build()
    {
        Err(error) => {
            error!(
                ?error,
                "Failed to build thread pool builder running op uncontrolled"
            );
            op()
        }
        Ok(pool) => pool.install(op),
    }
}
