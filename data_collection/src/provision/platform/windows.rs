use logger::error;
use serde::Deserialize;
use std::{collections::HashMap, ffi::OsStr, mem::MaybeUninit, os::windows::ffi::OsStrExt};
use windows::{
    core::PCWSTR,
    Win32::{
        Storage::FileSystem::{
            GetFileVersionInfoSizeW, GetFileVersionInfoW, VerQueryValueW, VS_FIXEDFILEINFO,
        },
        System::SystemInformation::GetPhysicallyInstalledSystemMemory,
    },
};
use windows_patch_xml_checker::WMIGetter;
use windows_registry::WinRegistry;

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_OperatingSystem")]
#[serde(rename_all = "PascalCase")]
struct OsScannedValue {
    version: String,
    caption: String,
}

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_Processor")]
#[serde(rename_all = "PascalCase")]
struct CpuScannedValue {
    architecture: Option<i16>,
    name: String,
    number_of_logical_processors: u32,
    number_of_cores: u32,
}

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_ComputerSystem")]
#[serde(rename_all = "PascalCase")]
struct WinSystemScannedValue {
    model: String,
    manufacturer: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename = "Win32_ComputerSystemProduct")]
#[serde(rename_all = "PascalCase")]
struct UuidScannedValue {
    uuid: String,
}

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_Bios")]
#[serde(rename_all = "PascalCase")]
struct BiosSystemScannedValue {
    serial_number: String,
    manufacturer: String,
}

pub fn get_os_version() -> HashMap<String, String> {
    //name,version,major,minor,patch,build, platform,platformlike
    let mut value_map = HashMap::new();
    value_map.insert("platform".to_owned(), "windows".to_owned());
    value_map.insert("platform_like".to_owned(), "windows".to_owned());

    match WMIGetter::new(None) {
        Ok(wmi_connection) => match wmi_connection.get::<OsScannedValue>() {
            Some(result) => {
                value_map.insert("name".to_owned(), result.caption.to_string());
                let version_parts = result.version.split(".").collect::<Vec<&str>>();
                value_map.insert("major".to_owned(), version_parts[0].to_owned());
                if version_parts.len() > 1 {
                    value_map.insert("minor".to_owned(), version_parts[1].to_owned());
                }
                if version_parts.len() > 2 {
                    value_map.insert("build".to_owned(), version_parts[2].to_owned());
                    value_map.insert("patch".to_owned(), version_parts[2].to_owned());
                }
                match WinRegistry::new(
                    "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion",
                ) {
                    Ok(registry) => {
                        let revision: u32 = registry.get_value("UBR".to_owned());
                        value_map.insert(
                            "version".to_owned(),
                            format!("{}.{}", result.version, revision),
                        );
                        value_map.insert(
                            "display_version".to_owned(),
                            registry.get_value("DisplayVersion".to_owned()),
                        );
                    }
                    _ => {
                        value_map.insert("version".to_owned(), result.version.to_string());
                    }
                }
            }
            _ => {}
        },
        Err(error) => {
            error!(?error, "Failed to open wmi connection to read system value");
        }
    };

    value_map
}

fn get_kernel_version() -> Option<String> {
    unsafe {
        // Path to the file
        let file_path = OsStr::new("C:\\Windows\\System32\\ntoskrnl.exe")
            .encode_wide()
            .chain(Some(0).into_iter())
            .collect::<Vec<_>>();

        // Get the size of the version info
        let size = GetFileVersionInfoSizeW(PCWSTR(file_path.as_ptr()), None);
        if size == 0 {
            error!("Failed to get version info size");
            return None;
        }

        // Allocate buffer for version info
        let mut buffer = vec![0u8; size as usize];

        // Get the version info
        if GetFileVersionInfoW(
            PCWSTR(file_path.as_ptr()),
            None,
            size,
            buffer.as_mut_ptr() as *mut _,
        )
        .is_err()
        {
            error!("Failed to get version info");
            return None;
        }

        // Query the version value
        let mut ffi: *mut VS_FIXEDFILEINFO = std::ptr::null_mut();
        let mut len: u32 = 0;
        if VerQueryValueW(
            buffer.as_ptr() as *const _,
            PCWSTR("\\\0".encode_utf16().collect::<Vec<_>>().as_ptr()),
            &mut ffi as *mut _ as *mut _,
            &mut len,
        )
        .as_bool()
            == false
        {
            error!("Failed to query version value");
            return None;
        }

        // Print version info
        if ffi.is_null() {
            error!("FFI call received null");
            return None;
        }
        let ffi = &*ffi;
        let file_version = (
            (ffi.dwProductVersionMS >> 16) & 0xffff,
            (ffi.dwProductVersionMS >> 0) & 0xffff,
            (ffi.dwProductVersionLS >> 16) & 0xffff,
            (ffi.dwProductVersionLS >> 0) & 0xffff,
        );
        Some(format!(
            "{}.{}.{}.{}",
            file_version.0, file_version.1, file_version.2, file_version.3
        ))
    }
}

pub fn get_system_uuid() -> Option<String> {
    match WMIGetter::new(None) {
        Ok(wmi_connection) => match wmi_connection.get::<UuidScannedValue>() {
            Some(value) => Some(value.uuid.to_string()),
            _ => None,
        },
        _ => None,
    }
}

pub fn get_system_info() -> HashMap<String, String> {
    let mut value_map = HashMap::new();
    if let Some(kernel_version) = get_kernel_version() {
        value_map.insert("kernel_version".to_owned(), kernel_version);
    }

    match WMIGetter::new(None) {
        Ok(wmi_connection) => {
            match wmi_connection.get::<CpuScannedValue>() {
                Some(value) => {
                    if let Some(arch) = value.architecture {
                        value_map.insert(
                            "cpu_type".to_owned(),
                            match arch {
                                9 => "x86_64",
                                5 => "ARM",
                                6 => "x64 Itanium",
                                0 => "x86",
                                _ => "Unknown",
                            }
                            .to_owned(),
                        );
                    }
                    value_map.insert("cpu_brand".to_owned(), value.name.to_string());
                    value_map.insert(
                        "cpu_physical_cores".to_owned(),
                        value.number_of_cores.to_string(),
                    );
                    value_map.insert(
                        "cpu_logical_cores".to_owned(),
                        value.number_of_logical_processors.to_string(),
                    );
                }
                _ => {}
            };
            match wmi_connection.get::<WinSystemScannedValue>() {
                Some(value) => {
                    value_map.insert("hardware_vendor".to_owned(), value.manufacturer.to_string());
                    value_map.insert("hardware_model".to_owned(), value.model.to_string());
                }
                _ => {}
            };
            match wmi_connection.get::<BiosSystemScannedValue>() {
                Some(value) => {
                    value_map.insert(
                        "hardware_serial".to_owned(),
                        value.serial_number.to_string(),
                    );
                    value_map.insert("platform_vendor".to_owned(), value.manufacturer.to_string());
                }
                _ => {}
            };
            match wmi_connection.get::<UuidScannedValue>() {
                Some(value) => {
                    value_map.insert("uuid".to_owned(), value.uuid.to_string());
                }
                _ => {}
            };
        }
        _ => {}
    };

    let memory = unsafe {
        let mut memory = MaybeUninit::uninit();
        match GetPhysicallyInstalledSystemMemory(memory.as_mut_ptr()) {
            Ok(_) => memory.assume_init(),
            Err(_) => 0,
        }
    };
    value_map.insert("physical_memory".to_owned(), (memory * 1024).to_string());

    value_map
}
