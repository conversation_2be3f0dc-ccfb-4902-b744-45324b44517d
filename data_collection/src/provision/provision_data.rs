use super::{os_version::OsVers<PERSON>, system_info::SystemInfo};
use serde::{Deserialize, Serialize};

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ProvisionData {
    uuid: String,
    system_uuid: String,
    agent_version: String,
    enroll_secret: String,
    os: OsVersion,
    platform: SystemInfo,
}

impl ProvisionData {
    pub fn collect(uuid: String, enroll_secret: String) -> Self {
        Self {
            uuid,
            system_uuid: ProvisionData::system_uuid(),
            enroll_secret,
            agent_version: env!("CARGO_PKG_VERSION").to_string(),
            os: OsVersion::collect(),
            platform: SystemInfo::collect(),
        }
    }

    fn system_uuid() -> String {
        #[cfg(windows)]
        {
            use crate::provision::platform::get_system_uuid;
            return get_system_uuid().unwrap_or_default();
        }
        #[cfg(not(windows))]
        {
            use logger::error;
            use shell::ShellCommand;
            use utils::runtime::create_new_runtime;
            let mut cmd = if cfg!(target_os = "macos") {
                ShellCommand::new("system_profiler SPHardwareDataType | awk '/UUID/ { print $NF }'")
            } else if cfg!(target_os = "linux") {
                ShellCommand::new("dmidecode -s system-uuid")
            } else {
                error!("Failed to read UUID for current platform");
                return String::new();
            };

            let result = create_new_runtime().block_on(async { cmd.run().await });

            if let Err(error) = result {
                error!(?error, "Error getting system UUID");
                return String::new();
            }

            let cmd_result = result.unwrap();

            if cmd_result.exit_code != 0 {
                error!(?cmd_result, "Error getting system UUID");
                return String::new();
            }

            return cmd_result.output;
        }
    }
}
