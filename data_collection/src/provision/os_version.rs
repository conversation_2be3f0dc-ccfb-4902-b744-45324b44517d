use super::platform::get_os_version;
use serde::{Deserialize, Serialize};
use sysinfo::System;

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct OsVersion {
    host_name: String,
    name: String,
    major: String,
    minor: String,
    arch: String,
    platform: String,
    platform_like: String,
    os_version: String,
    build: String,
    patch: String,
    code_name: String,
    display_version: String,
}

impl OsVersion {
    pub fn collect() -> Self {
        let mut version_dict = get_os_version();
        Self {
            host_name: System::host_name().unwrap_or("".to_owned()),
            name: version_dict.remove("name").unwrap_or("".to_owned()),
            os_version: version_dict.remove("version").unwrap_or("".to_owned()),
            major: version_dict.remove("major").unwrap_or("".to_owned()),
            minor: version_dict.remove("minor").unwrap_or("".to_owned()),
            build: version_dict.remove("build").unwrap_or("".to_owned()),
            patch: version_dict.remove("patch").unwrap_or("".to_owned()),
            display_version: version_dict
                .remove("display_version")
                .unwrap_or("".to_owned()),
            platform: version_dict.remove("platform").unwrap_or("".to_owned()),
            platform_like: version_dict
                .remove("platform_like")
                .unwrap_or("".to_owned()),
            arch: System::cpu_arch(),
            code_name: version_dict
                .remove("code_name")
                .unwrap_or(System::long_os_version().unwrap_or_default()),
            ..Default::default()
        }
    }
}
