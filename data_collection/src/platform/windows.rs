use logger::error;
use windows::{
    core::{<PERSON>WSTR, PWSTR},
    Win32::{
        Foundation::{LocalFree, HLOCAL},
        Security::{Authorization::ConvertStringSidToSidW, LookupAccountSidW, PSID, SID_NAME_USE},
    },
};

pub fn get_bssid() -> Option<String> {
    None
}

pub fn get_username_from_sid(sid_str: &str) -> String {
    let mut sid: PSID = PSID::default();

    unsafe {
        // Convert string SID to PSID
        if let Err(error) = ConvertStringSidToSidW(
            PCWSTR(
                sid_str
                    .encode_utf16()
                    .chain([0])
                    .collect::<Vec<u16>>()
                    .as_ptr(),
            ),
            &mut sid,
        ) {
            error!(?error, "Failed to convert {} to sid", sid_str);
            return "".to_owned();
        }

        let mut name = [0u16; 256];
        let mut name_size = name.len() as u32;
        let mut domain = [0u16; 256];
        let mut domain_size = domain.len() as u32;
        let mut sid_type = SID_NAME_USE(0);

        // Lookup account name
        if let Err(error) = LookupAccountSidW(
            None,
            sid,
            Some(PWSTR(name.as_mut_ptr())),
            &mut name_size,
            Some(PWSTR(domain.as_mut_ptr())),
            &mut domain_size,
            &mut sid_type,
        ) {
            error!(?error, "Failed to resolve name domain");
            LocalFree(Some(HLOCAL(sid.0))); // Ensure memory is freed in case of failure
            return "".to_owned();
        }
        let name = String::from_utf16_lossy(&name[..name_size as usize]);
        let _domain = String::from_utf16_lossy(&domain[..domain_size as usize]);

        LocalFree(Some(HLOCAL(sid.0)));
        return name;
    }
}
