use logger::error;
use std::collections::HashSet;
use zbus::{
    blocking::{Connection, Proxy},
    zvariant::OwnedObjectPath,
};

// #[proxy(
//     interface = "org.freedesktop.systemd1.Manager",
//     default_service = "org.freedesktop.systemd1",
//     default_path = "/org/freedesktop/systemd1"
// )]
// trait Systemd {
//     fn list_units(
//         &self,
//     ) -> zbus::Result<
//         Vec<(
//             String,
//             String,
//             String,
//             String,
//             String,
//             String,
//             String,
//             u32,
//             String,
//         )>,
//     >;
// }

#[derive(Debug, Default, PartialEq, Eq, Hash)]
pub struct SystemdItem {
    pub name: String,
    pub description: String,
    pub load_state: String,
    pub status: String,
    pub path: String,
}

pub fn systemd_units() -> HashSet<SystemdItem> {
    // First open up a connection to the session bus.
    // Connect to the system D-Bus
    let connection = match Connection::system() {
        Ok(con) => con,
        Err(error) => {
            error!(?error, "Failed to establish systemd connection");
            return HashSet::new();
        }
    };

    // Create a proxy for the Systemd Manager
    let proxy = match Proxy::new(
        &connection,
        "org.freedesktop.systemd1",         // D-Bus service
        "/org/freedesktop/systemd1",        // Object path
        "org.freedesktop.systemd1.Manager", // Interface
    ) {
        Ok(p) => p,
        Err(error) => {
            error!(?error, "Failed to create proxy");
            return HashSet::new();
        }
    };

    let systemd_units: Vec<(
        String,
        String,
        String,
        String,
        String,
        String,
        OwnedObjectPath,
        u32,
        String,
        OwnedObjectPath,
    )> = match proxy.call("ListUnits", &()) {
        Ok(units) => units,
        Err(error) => {
            error!(?error, "Failed to get systemd units from proxy");
            return HashSet::new();
        }
    };

    let mut units = HashSet::new();

    for unit in systemd_units {
        units.insert(SystemdItem {
            name: unit.0,
            description: unit.1,
            load_state: unit.2,
            status: unit.3.into(),
            path: unit.6.to_string(),
        });
    }

    units
}

pub fn get_bssid() -> Option<String> {
    None
}
