use crate::execute_in_thread_pool;
use glob::glob;
use logger::{error, trace};
use plist::Value;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use shell::ShellCommand;
use std::{
    collections::{HashMap, HashSet},
    fs,
    io::{<PERSON>uf<PERSON><PERSON>, BufReader},
    path::Path,
};
use utils::runtime::create_new_runtime;

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct ServiceItem {
    pub name: String,
    pub path: String,
    pub current_status: String,
    pub service_status: String,
    pub run_at_load: bool,
}

#[derive(Debug)]
struct LaunchCtlItem {
    label: String,
    pid: String,
}

fn build_service_item(
    path: &Path,
    launchctl_list: &HashMap<String, LaunchCtlItem>,
) -> Option<ServiceItem> {
    let value = match Value::from_file(path) {
        Ok(value) => value,
        Err(error) => {
            error!(
                ?error,
                "Failed to parse plist file at path {}",
                path.display()
            );
            return None;
        }
    };
    let value = match value.as_dictionary() {
        Some(value) => value,
        None => {
            error!("Failed to get plist file content as dictionary");
            return None;
        }
    };
    let mut item = ServiceItem::default();
    if let Some(value) = value.get("RunAtLoad").or(Some(&Value::Boolean(false))) {
        item.run_at_load = value.as_boolean().unwrap_or_default();
    }
    if let Some(name) = value.get("Label") {
        item.name = name.as_string().unwrap().to_owned();
        item.service_status = if launchctl_list.contains_key(&item.name) {
            "Enable"
        } else {
            "Disable"
        }
        .to_string();
        item.current_status = if let Some(launch_ctl_item) = launchctl_list.get(&item.name) {
            if launch_ctl_item.pid != "-" {
                "Running"
            } else {
                "Stopped"
            }
        } else {
            "Stopped"
        }
        .to_owned();
    } else if let Some(name) = value.get("Title") {
        item.name = name.as_string().unwrap().to_owned();
        item.service_status = if launchctl_list.contains_key(&item.name) {
            "Enable"
        } else {
            "Disable"
        }
        .to_string();
        item.current_status = if let Some(launch_ctl_item) = launchctl_list.get(&item.name) {
            if launch_ctl_item.pid != "-" {
                "Running"
            } else {
                "Stopped"
            }
        } else {
            "Stopped"
        }
        .to_owned();
    }
    if let Some(args) = value.get("Program").or(value.get("ProgramArguments")) {
        let program = args
            .as_array()
            .or(Some(&vec![args.to_owned()]))
            .and_then(|item| item.first())
            .and_then(|value| value.as_string())
            .and_then(|value| Some(value.to_owned()));
        if let Some(program_path) = program {
            item.path = program_path;
        }
    }
    Some(item)
}

fn scan_folder(
    path: &Path,
    launchctl_list: &HashMap<String, LaunchCtlItem>,
) -> HashSet<ServiceItem> {
    trace!("Scanning folder {}", path.display());
    let dir = match fs::read_dir(path) {
        Ok(dir) => dir,
        Err(error) => {
            error!(
                ?error,
                "Failed to read directory at path {}",
                path.display()
            );
            return HashSet::new();
        }
    };
    dir.into_iter()
        .filter_map(Result::ok)
        .map(|entry| {
            trace!("Checking file {}", entry.path().display());
            build_service_item(entry.path().as_path(), launchctl_list)
        })
        .filter(|item| item.is_some())
        .map(|item| item.unwrap())
        .collect()
}

pub fn get_all_services() -> HashSet<ServiceItem> {
    let service_paths = [
        "/Library/LaunchAgents",
        "/Library/LaunchDaemons",
        "/System/Library/LaunchAgents",
        "/System/Library/LaunchDaemons",
        "/Users/<USER>/Library/LaunchAgents",
    ];

    let launchctl_list = match create_new_runtime()
        .block_on(async { ShellCommand::new("launchctl list").run().await })
    {
        Ok(output) => {
            let reader = BufReader::new(output.output.as_bytes());
            reader
                .lines()
                .into_iter()
                .filter_map(Result::ok)
                .map(|line| {
                    let parts = line.split_whitespace().collect::<Vec<&str>>();
                    if parts.len() == 3 {
                        Some(LaunchCtlItem {
                            label: parts[2].to_string(),
                            pid: parts[0].to_string(),
                        })
                    } else {
                        None
                    }
                })
                .filter(|item| item.is_some())
                .map(|i| {
                    let item = i.unwrap();
                    (item.label.to_owned(), item)
                })
                .collect::<HashMap<String, LaunchCtlItem>>()
        }
        Err(error) => {
            error!(?error, "Falied to get launchctl list output");
            HashMap::new()
        }
    };

    execute_in_thread_pool(|| {
        service_paths
            .into_par_iter()
            .map(|item| glob(item))
            .filter(|result| {
                if let Err(error) = result {
                    error!(?error, "Failed to generate glob paths");
                    return false;
                }
                return true;
            })
            .map(|item| item.unwrap())
            .flat_map_iter(|paths| paths.into_iter())
            .filter_map(Result::ok)
            .flat_map(|path| scan_folder(path.as_path(), &launchctl_list))
            .collect()
    })
}

pub fn get_bssid() -> Option<String> {
    None
}
