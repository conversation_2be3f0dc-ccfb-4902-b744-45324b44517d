use crate::{
    execute_in_thread_pool, platform::get_all_services, start_up_items::start_up_item::StartUpItem,
};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use std::collections::HashSet;
use utils::shutdown::is_system_running;

pub fn get_start_up_items() -> HashSet<StartUpItem> {
    execute_in_thread_pool(|| {
        get_all_services()
            .into_par_iter()
            .take_any_while(|_| is_system_running())
            .filter(|item| item.run_at_load)
            .map(|item| {
                StartUpItem::default()
                    .name(item.name)
                    .path(item.path)
                    .status(item.service_status.into())
            })
            .collect()
    })
}
