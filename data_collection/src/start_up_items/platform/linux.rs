use crate::{
    execute_in_thread_pool, platform::systemd_units, start_up_items::start_up_item::StartUpItem,
};
use glob::glob;
use logger::{error, trace};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use std::{
    collections::HashSet,
    fs::{self, File},
    io::{BufRead, BufReader},
    path::Path,
};
use utils::shutdown::is_system_running;

fn extract_desktop_file(path: &Path) -> Option<StartUpItem> {
    let file = match File::open(&path) {
        Ok(file) => file,
        Err(error) => {
            error!(?error, "Failed to open desktop file {}", path.display());
            return None;
        }
    };
    let reader = BufReader::new(file);
    let lines = reader
        .lines()
        .into_iter()
        .filter(|line| {
            line.as_ref().is_ok_and(|item| {
                item.contains("Name=")
                    || item.contains("name=")
                    || item.contains("Exec=")
                    || item.contains("exec=")
            })
        })
        .map(|item| item.unwrap())
        .collect::<Vec<String>>();

    if lines.len() > 1 {
        let mut item = StartUpItem::default();
        for line in lines {
            let parts = line.split("=").collect::<Vec<_>>();
            if parts[0].to_lowercase() == "name" {
                item = item.name(parts[1].to_owned());
            } else {
                item = item.path(parts[1].to_owned());
            }
        }
        return Some(item);
    }

    None
}

fn scan_folder(path: &Path) -> HashSet<StartUpItem> {
    trace!("Scanning folder {}", path.display());
    let dir = match fs::read_dir(&path) {
        Ok(dir) => dir,
        Err(error) => {
            error!(?error, "Failed to read directory {}", path.display());
            return HashSet::new();
        }
    };

    dir.into_iter()
        .filter(|entry| entry.as_ref().is_ok_and(|i| i.path().is_file()))
        .map(|entry| entry.unwrap())
        .map(|entry| {
            if entry.path().extension().is_some_and(|ext| ext == "desktop") {
                extract_desktop_file(entry.path().as_path())
            } else {
                Some(
                    StartUpItem::default()
                        .name(
                            entry
                                .path()
                                .file_name()
                                .unwrap_or_default()
                                .to_string_lossy()
                                .to_string(),
                        )
                        .path(entry.path().to_string_lossy().to_string()),
                )
            }
        })
        .filter(|item| item.is_some())
        .map(|item| item.unwrap())
        .collect()
}

fn from_systemd_units() -> HashSet<StartUpItem> {
    systemd_units()
        .into_iter()
        .filter(|item| item.load_state == "loaded")
        .map(|item| {
            StartUpItem::default()
                .name(item.name)
                .status(item.status.into())
                .path(item.path)
        })
        .collect()
}

pub fn get_start_up_items() -> HashSet<StartUpItem> {
    let startup_paths = [
        "/etc/xdg/autostart",
        "/usr/share/autostart",
        "/etc/init.d/",
        "/home/<USER>/.config/autostart",
        "/home/<USER>/.config/autostart-scripts",
    ];

    execute_in_thread_pool(|| {
        startup_paths
            .into_par_iter()
            .take_any_while(|_| is_system_running())
            .map(|item| glob(&item))
            .filter(|item| {
                if let Err(error) = item.as_ref() {
                    error!(?error, "Failed to generate glob path");
                    return false;
                }
                return true;
            })
            .map(|item| item.unwrap())
            .flat_map_iter(|paths| paths.into_iter())
            .filter_map(Result::ok)
            .flat_map(|path| scan_folder(path.as_path()))
            .chain(from_systemd_units())
            .collect::<HashSet<StartUpItem>>()
    })
}
