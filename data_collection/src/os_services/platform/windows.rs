use crate::os_services::os_service::OsService;
use logger::error;
use std::collections::HashSet;
use windows::Win32::System::Services::{
    CloseServiceHandle, EnumServicesStatusExW, OpenSCManagerW, OpenServiceW, QueryServiceConfigW,
    ENUM_SERVICE_STATUS_PROCESSW, QUERY_SERVICE_CONFIGW, SC_ENUM_PROCESS_INFO, SC_HANDLE,
    SC_MANAGER_ENUMERATE_SERVICE, SERVICE_AUTO_START, SERVICE_BOOT_START, SERVICE_CONTINUE_PENDING,
    SERVICE_DEMAND_START, SERVICE_DISABLED, SERVICE_PAUSED, SERVICE_PAUSE_PENDING,
    SERVICE_QUERY_CONFIG, SERVICE_RUNNING, SERVICE_START_PENDING, SERVICE_STATE_ALL,
    SERVICE_STOPPED, SERVICE_STOP_PENDING, SERVICE_SYSTEM_START, SERVICE_WIN32,
};

fn parse_service(scm: SC_HANDLE, service_info: &ENUM_SERVICE_STATUS_PROCESSW) -> OsService {
    let mut os_service = OsService::default();
    os_service.name = unsafe { service_info.lpServiceName.to_string().unwrap_or_default() };
    os_service.description = unsafe { service_info.lpDisplayName.to_string().unwrap_or_default() };
    os_service.status = match service_info.ServiceStatusProcess.dwCurrentState {
        SERVICE_STOPPED => "Stopped",
        SERVICE_START_PENDING => "Start Pending",
        SERVICE_STOP_PENDING => "Stop Pending",
        SERVICE_RUNNING => "Running",
        SERVICE_CONTINUE_PENDING => "Continue Pending",
        SERVICE_PAUSE_PENDING => "Pause Pending",
        SERVICE_PAUSED => "Paused",
        _ => "Unknown",
    }
    .to_owned();

    let service_handle =
        match unsafe { OpenServiceW(scm, service_info.lpServiceName, SERVICE_QUERY_CONFIG) } {
            Ok(handle) => handle,
            Err(error) => {
                error!(?error, "Failed to open service details {}", os_service.name);
                return os_service;
            }
        };
    let mut config_bytes_needed: u32 = 0;

    // Get the required buffer size for the service configuration
    unsafe {
        if let Err(error) = QueryServiceConfigW(service_handle, None, 0, &mut config_bytes_needed) {
            if error.code().to_string() != "0x8007007A" {
                error!(?error, "Failed to query service config required bytes");
                return os_service;
            }
        }
    };

    // Allocate buffer for the service configuration
    let mut config_buffer: Vec<u8> = vec![0; config_bytes_needed as usize];

    // Get the actual service configuration
    let service_config: *const QUERY_SERVICE_CONFIGW = config_buffer.as_mut_ptr() as *const _;
    unsafe {
        if let Err(error) = QueryServiceConfigW(
            service_handle,
            Some(config_buffer.as_mut_ptr() as *mut _),
            config_buffer.len() as u32,
            &mut config_bytes_needed,
        ) {
            // "The data area passed to a system call is too small." unexepcted
            if error.code().to_string() != "0x8007007A" {
                error!(?error, "Failed to read query service config");
                return os_service;
            }
        }
    }

    let service_config_data = unsafe { *service_config };

    os_service.cmdline = unsafe {
        service_config_data
            .lpBinaryPathName
            .to_string()
            .unwrap_or_default()
    };

    os_service.start_type = match service_config_data.dwStartType {
        SERVICE_AUTO_START => "Automatic",
        SERVICE_BOOT_START => "Boot",
        SERVICE_DEMAND_START => "Manual",
        SERVICE_DISABLED => "Disabled",
        SERVICE_SYSTEM_START => "System",
        _ => "Unknown",
    }
    .to_owned();

    unsafe {
        let _ = CloseServiceHandle(service_handle);
    };
    os_service
}

pub fn get_os_services() -> HashSet<OsService> {
    let mut services = HashSet::new();

    unsafe {
        let scm = match OpenSCManagerW(None, None, SC_MANAGER_ENUMERATE_SERVICE) {
            Ok(scm) => scm,
            Err(error) => {
                error!(?error, "Failed to open service manager");
                return services;
            }
        };

        let mut bytes_needed: u32 = 0;
        let mut services_returned: u32 = 0;
        let mut resume_handle: u32 = 0;

        // Get the required buffer size
        if let Err(error) = EnumServicesStatusExW(
            scm,
            SC_ENUM_PROCESS_INFO,
            SERVICE_WIN32,
            SERVICE_STATE_ALL,
            None,
            &mut bytes_needed,
            &mut services_returned,
            Some(&mut resume_handle),
            None,
        ) {
            // "The more data available" unexpected
            if error.code().to_string() != "0x800700EA" {
                error!(?error, "Failed to get bytes needed to read data");
                let _ = CloseServiceHandle(scm);
                return services;
            }
        }

        // Allocate buffer
        let mut buffer: Vec<u8> = vec![0; bytes_needed as usize];

        // Get the actual data
        if let Err(error) = EnumServicesStatusExW(
            scm,
            SC_ENUM_PROCESS_INFO,
            SERVICE_WIN32,
            SERVICE_STATE_ALL,
            Some(&mut buffer),
            &mut bytes_needed,
            &mut services_returned,
            Some(&mut resume_handle),
            None,
        ) {
            error!(?error, "Failed to get data of services");
            let _ = CloseServiceHandle(scm);
            return services;
        }

        let win_services: &[ENUM_SERVICE_STATUS_PROCESSW] = std::slice::from_raw_parts(
            buffer.as_ptr() as *const ENUM_SERVICE_STATUS_PROCESSW,
            services_returned as usize,
        );

        for service in win_services {
            services.insert(parse_service(scm, service));
        }

        let _ = CloseServiceHandle(scm);
    }

    services
}
