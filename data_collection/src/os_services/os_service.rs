use logger::debug;
use serde::Serialize;
use std::collections::HashSet;

use crate::os_services::platform::get_os_services;

#[derive(Debug, Serialize, PartialEq, Eq, Hash, Default)]
pub struct OsService {
    pub name: String,
    pub description: String,
    pub status: String,
    pub cmdline: String,
    pub start_type: String,
}

impl OsService {
    pub fn collect() -> HashSet<Self> {
        let os_services = get_os_services();

        debug!("Collected total {} os services", os_services.len(),);

        os_services
    }
}
