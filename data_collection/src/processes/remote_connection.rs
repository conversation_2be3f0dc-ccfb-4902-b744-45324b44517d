use crate::execute_in_thread_pool;
use logger::{debug, error};
use netstat2::{
    get_sockets_info, AddressFamilyFlags, ProtocolFlags, ProtocolSocketInfo, SocketInfo, TcpState,
};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use std::{
    collections::HashSet,
    net::{IpAddr, Ipv4Addr},
};
use sysinfo::System;
use utils::shutdown::is_system_running;

#[derive(Debug, Serialize, Eq, PartialEq, Hash, Default)]
pub enum Protocol {
    #[default]
    Tcp,
    Udp,
}

#[derive(Debug, Serialize, PartialEq, Eq, Hash)]
pub enum ConnectionState {
    Closed,
    Listen,
    SynSent,
    SynReceived,
    Established,
    FinWait1,
    FinWait2,
    CloseWait,
    Closing,
    LastAck,
    TimeWait,
    DeleteTcb,
    Unknown,
}

impl From<TcpState> for ConnectionState {
    fn from(value: TcpState) -> Self {
        match value {
            TcpState::Closed => ConnectionState::Closed,
            TcpState::Listen => ConnectionState::Listen,
            TcpState::SynSent => ConnectionState::SynSent,
            TcpState::SynReceived => ConnectionState::SynReceived,
            TcpState::Established => ConnectionState::Established,
            TcpState::FinWait1 => ConnectionState::FinWait1,
            TcpState::FinWait2 => ConnectionState::FinWait2,
            TcpState::CloseWait => ConnectionState::CloseWait,
            TcpState::Closing => ConnectionState::Closing,
            TcpState::LastAck => ConnectionState::LastAck,
            TcpState::TimeWait => ConnectionState::TimeWait,
            TcpState::DeleteTcb => ConnectionState::DeleteTcb,
            TcpState::Unknown => ConnectionState::Unknown,
        }
    }
}

#[derive(Debug, Serialize, PartialEq, Eq, Hash)]
pub struct RemoteConnection {
    pid: u32,
    name: String,
    local_address: IpAddr,
    remote_address: IpAddr,
    local_port: u16,
    remote_port: u16,
    state: Option<ConnectionState>,
    protocol: Protocol,
}

struct RemoteConnectionContainer<'a> {
    si: SocketInfo,
    system: &'a System,
}

impl From<RemoteConnectionContainer<'_>> for RemoteConnection {
    fn from(value: RemoteConnectionContainer<'_>) -> Self {
        let si = value.si;
        let system = value.system;
        let pid = si.associated_pids.first().map_or(0, |i| i.to_owned());
        let process_name = match system.process((pid as usize).into()) {
            Some(process) => process.name().to_string_lossy().to_string(),
            None => "".to_string(),
        };
        match si.protocol_socket_info {
            ProtocolSocketInfo::Tcp(tcp_si) => RemoteConnection {
                name: process_name,
                remote_address: tcp_si.remote_addr,
                remote_port: tcp_si.remote_port,
                local_address: tcp_si.local_addr,
                local_port: tcp_si.local_port,
                protocol: Protocol::Tcp,
                state: Some(tcp_si.state.into()),
                pid,
            },
            ProtocolSocketInfo::Udp(udp_si) => RemoteConnection {
                pid,
                name: process_name,
                local_address: udp_si.local_addr,
                remote_address: Ipv4Addr::from(0_u32).into(),
                local_port: udp_si.local_port,
                remote_port: 0,
                state: None,
                protocol: Protocol::Udp,
            },
        }
    }
}

impl RemoteConnection {
    pub fn collect(system: &System) -> HashSet<Self> {
        let af_flags = AddressFamilyFlags::IPV4 | AddressFamilyFlags::IPV6;
        let proto_flags = ProtocolFlags::TCP | ProtocolFlags::UDP;
        let sockets_info = match get_sockets_info(af_flags, proto_flags) {
            Ok(sockets) => sockets,
            Err(error) => {
                error!(?error, "Failed to get socket informations");
                return HashSet::new();
            }
        };

        let connections = execute_in_thread_pool(|| {
            sockets_info
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .filter(|item| match &item.protocol_socket_info {
                    ProtocolSocketInfo::Tcp(tcp_socket_info) => {
                        tcp_socket_info.state != TcpState::Listen
                            && tcp_socket_info.local_addr != tcp_socket_info.remote_addr
                    }
                    ProtocolSocketInfo::Udp(_) => true,
                })
                .map(|si| RemoteConnectionContainer {
                    si,
                    system: &system,
                })
                .map(|item| item.into())
                .collect::<HashSet<RemoteConnection>>()
        });

        debug!("Collected total {} remote connections", connections.len(),);

        connections
    }
}
