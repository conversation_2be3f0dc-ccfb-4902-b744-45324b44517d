use crate::execute_in_thread_pool;
use logger::{debug, error};
use netstat2::{
    get_sockets_info, AddressFamilyFlags, ProtocolFlags, ProtocolSocketInfo, SocketInfo, TcpState,
};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use std::{collections::HashSet, net::IpAddr};
use sysinfo::System;
use utils::shutdown::is_system_running;

#[derive(Debug, Serialize, Eq, PartialEq, Hash, Default)]
pub enum Protocol {
    #[default]
    Tcp,
    Udp,
}
#[derive(Debug, Serialize, PartialEq, Eq, Hash)]
pub struct ListeningPort {
    pid: u32,
    name: String,
    address: IpAddr,
    port: u16,
    protocol: Protocol,
}

struct ListeningPortContainer<'a> {
    si: SocketInfo,
    system: &'a System,
}

impl From<ListeningPortContainer<'_>> for ListeningPort {
    fn from(value: ListeningPortContainer) -> Self {
        let si = value.si;
        let system = value.system;
        let pid = si.associated_pids.first().map_or(0, |i| i.to_owned());
        let process_name = match system.process((pid as usize).into()) {
            Some(process) => process.name().to_string_lossy().to_string(),
            None => "".to_string(),
        };
        match si.protocol_socket_info {
            ProtocolSocketInfo::Tcp(tcp_si) => ListeningPort {
                name: process_name,
                address: tcp_si.local_addr,
                port: tcp_si.local_port,
                protocol: Protocol::Tcp,
                pid,
            },
            ProtocolSocketInfo::Udp(udp_si) => ListeningPort {
                pid,
                name: process_name,
                address: udp_si.local_addr,
                port: udp_si.local_port,
                protocol: Protocol::Udp,
            },
        }
    }
}

impl ListeningPort {
    pub fn collect(system: &System) -> HashSet<Self> {
        let af_flags = AddressFamilyFlags::IPV4 | AddressFamilyFlags::IPV6;
        let proto_flags = ProtocolFlags::TCP | ProtocolFlags::UDP;

        let sockets_info = match get_sockets_info(af_flags, proto_flags) {
            Ok(sockets) => sockets,
            Err(error) => {
                error!(?error, "Failed to get socket informations");
                return HashSet::new();
            }
        };

        let listening_ports = execute_in_thread_pool(|| {
            sockets_info
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .filter(|item| match &item.protocol_socket_info {
                    ProtocolSocketInfo::Tcp(tcp_socket_info) => {
                        tcp_socket_info.state == TcpState::Listen
                    }
                    ProtocolSocketInfo::Udp(_) => true,
                })
                .map(|si| ListeningPortContainer {
                    si,
                    system: &system,
                })
                .map(|item| item.into())
                .collect::<HashSet<ListeningPort>>()
        });

        debug!("Collected total {} listening ports", listening_ports.len(),);
        listening_ports
    }
}
