use super::platform::get_system_users;
use logger::debug;
use serde::Serialize;
use std::collections::HashSet;

#[derive(Debug, PartialEq, Eq, Serialize, Hash, Default)]
pub struct SystemUser {
    uid: u32,
    username: String,
    description: String,
    directory: String,
    shell: String,
}

impl SystemUser {
    pub fn collect() -> HashSet<Self> {
        let users = get_system_users();
        debug!("Collected total {} users", users.len());
        users
    }

    pub fn uid(mut self, uid: u32) -> Self {
        self.uid = uid;
        self
    }

    pub fn get_uid(&self) -> u32 {
        self.uid
    }

    pub fn get_username(&self) -> &str {
        &self.username
    }

    pub fn username(mut self, username: String) -> Self {
        self.username = username;
        self
    }

    pub fn description(mut self, description: String) -> Self {
        self.description = description;
        self
    }

    pub fn directory(mut self, directory: String) -> Self {
        self.directory = directory;
        self
    }

    pub fn shell(mut self, shell: String) -> Self {
        self.shell = shell;
        self
    }
}
