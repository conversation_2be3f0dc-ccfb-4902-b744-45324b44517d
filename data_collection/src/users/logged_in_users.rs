use super::platform::get_logged_in_users;
use logger::debug;
use serde::Serialize;
use std::collections::HashSet;

#[derive(Debug, Hash, PartialEq, Eq, Default, Serialize)]
pub struct LoggedInUser {
    pub r#type: String,
    pub user: String,
    pub tty: String,
    pub host: String,
    pub time: i64,
    pub pid: i32,
    pub sid: String,
    pub registry_hive: String,
}

impl LoggedInUser {
    pub fn collect() -> HashSet<LoggedInUser> {
        let users = get_logged_in_users();
        debug!("Collected total {} logged in users", users.len(),);
        users
    }
}
