use anyhow::Error as AnyhowError;
use shell::ShellError;
use thiserror::Error;
use tokio::task::Join<PERSON>rror;

#[derive(Error, Debug)]
pub enum AgentError {
    #[error("Agent Error: Failed to join task for agent")]
    TaskJoinError(#[from] JoinError),

    #[error("Agent Error: Agent {0} called wait before starting it")]
    TaskWaitBeforeStart(String),

    #[error("Agent Error: Agent {0} called abort before starting it")]
    TaskAbortBeforeStart(String),

    #[error("Agent Error: Shell Command error")]
    ShellCommandError(#[from] ShellError),

    #[error("Agent Error: Task execution error")]
    TaskExecutionError(#[from] AnyhowError),
}
