use crate::{<PERSON><PERSON><PERSON><PERSON>, AgentRunnable};
use anyhow::Result;
use logger::{debug, error, Level, WithSubscriber};
use std::sync::Arc;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;

pub struct Agent {
    name: String,
    inner: Arc<Box<dyn AgentRunnable>>,
    task_handle: Option<JoinHandle<Result<()>>>,
}

impl Agent {
    pub fn new(agent: Box<dyn AgentRunnable>) -> Self {
        Self {
            name: agent.get_name().to_owned(),
            inner: Arc::new(agent),
            task_handle: None,
        }
    }

    pub fn is_running(&self) -> bool {
        self.task_handle.is_some()
    }

    pub fn get_inner(&self) -> Arc<Box<dyn AgentRunnable>> {
        self.inner.clone()
    }

    pub fn get_name(&self) -> &String {
        &self.name
    }

    pub async fn set_log_level(&self, _level: Level) {
        // self.inner.set_log_level(level);
        // @TODO hrere need to find a way to mutate log level
    }

    pub fn start(&mut self) {
        let agent = Arc::clone(&self.inner);
        let logger = agent.logger();
        let subscriber = logger.subscriber();
        self.task_handle = Some(tokio::task::spawn(
            async move {
                let _guard = logger.guard();
                agent.start(logger).await
            }
            .with_subscriber(subscriber),
        ));
    }

    pub async fn wait(&mut self) -> Result<()> {
        if let Some(handle) = self.task_handle.take() {
            match handle.await {
                Ok(result) => {
                    debug!("Agent {} finished running", self.get_name());
                    result
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to complete task execution {}",
                        self.get_name()
                    );
                    Err(error.into())
                }
            }
        } else {
            Err(AgentError::TaskWaitBeforeStart(self.get_name().clone()).into())
        }
    }
}
