use super::Agent;
use logger::{error, info};

#[derive(Default)]
pub struct AgentManager {
    agents: Vec<Agent>,
}

impl AgentManager {
    pub fn add_agent(&mut self, agent: Agent) -> &AgentManager {
        self.agents.push(agent);
        self
    }

    pub async fn start(&mut self) {
        for agent in self.agents.iter_mut() {
            if agent.is_running() {
                continue;
            }
            info!("Agent Manager: Starting Agent {}", agent.get_name());

            agent.start();
        }

        for agent in self.agents.iter_mut() {
            let result = agent.wait().await;

            if result.is_err() {
                error!(
                    error = ?result.as_ref().err().unwrap(),
                    "Agent Manager: Failed to complete agent task for {}",
                    agent.get_name()
                );
            } else {
                info!(
                    "Agent Manager: agent {} finished executing",
                    agent.get_name()
                );
            }
        }
    }
}
