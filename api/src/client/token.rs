use crate::{ApiError, ApiOptions};
use logger::{debug, error, trace};
use reqwest::Response;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, time::Duration};

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default)]
pub struct Token {
    pub access_token: String,
    pub refresh_token: Option<String>,
}

impl Token {
    pub fn get_access_token(&self) -> String {
        self.access_token.clone()
    }

    pub async fn renew(options: &ApiOptions) -> Result<Token, ApiError> {
        let url = format!("{}/api/token", options.host().to_owned());
        let response: Result<Response, _> = reqwest::Client::builder()
            .danger_accept_invalid_certs(true)
            .build()?
            .post(&url)
            .timeout(Duration::from_secs(30))
            .json(&options.credentials())
            .send()
            .await;

        if response.is_err() {
            error!(
                "Failed to complete login request: {:?}",
                response.as_ref().err().as_ref().unwrap()
            );
            return Err(ApiError::LoginFailed);
        }

        let data: HashMap<String, String> = response.unwrap().json().await?;

        debug!("Token has been received successfully");

        let token: Token = Token {
            access_token: data.get("access-token").unwrap().to_string(),
            refresh_token: if data.get("refresh-token").is_some() {
                Some(data.get("refresh-token").unwrap().to_string())
            } else {
                None
            },
        };

        trace!("Got token: {:?}", token);

        Ok(token)
    }
}
