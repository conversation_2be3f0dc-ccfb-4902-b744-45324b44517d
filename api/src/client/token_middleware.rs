use super::Token;
use crate::{ApiError, ApiOptions};
use anyhow::anyhow;
use async_trait::async_trait;
use http::{header::AUTHORIZATION, Extensions, HeaderValue};
use logger::{debug, error, trace, WithSubscriber};
use reqwest::{Request, Response};
use reqwest_middleware::{Error, Middleware, Next, Result as RequestResult};
use std::sync::{Arc, OnceLock};
use tokio::sync::RwLock;

pub static API_TOKEN: OnceLock<RwLock<Option<Token>>> = OnceLock::new();

#[derive(Debug)]
pub struct TokenMiddleware {
    api_options: Arc<ApiOptions>,
}

impl TokenMiddleware {
    pub fn new(api_options: ApiOptions) -> Self {
        let _ = API_TOKEN.set(RwLock::new(None));
        Self {
            api_options: Arc::new(api_options),
        }
    }

    async fn renew_token(&self) -> Result<Token, ApiError> {
        let current_access_token = API_TOKEN.get().unwrap();
        async {
            match Token::renew(&self.api_options).await {
                Ok(token) => {
                    trace!("Got access token {:?}", token);
                    let mut token_writable = current_access_token.write().await;
                    *token_writable = Some(token.to_owned());
                    Ok(token)
                }
                Err(error) => {
                    error!(?error, "Failed to get Access token");
                    let mut token_writable = current_access_token.write().await;
                    *token_writable = None;
                    Err(error)
                }
            }
        }
        .with_current_subscriber()
        .await
    }
}

#[async_trait]
impl Middleware for TokenMiddleware {
    async fn handle(
        &self,
        mut req: Request,
        extensions: &mut Extensions,
        next: Next<'_>,
    ) -> RequestResult<Response> {
        if req.headers().get(AUTHORIZATION).is_none() {
            let token = API_TOKEN.get().unwrap().read().await;
            if let Some(token) = token.as_ref() {
                trace!("Access Token is available so attaching it to request");
                req.headers_mut().append(
                    AUTHORIZATION,
                    HeaderValue::from_str(format!("Bearer {}", token.get_access_token()).as_str())
                        .unwrap(),
                );
            } else {
                debug!("Access Token is not found so adding it");
                drop(token);
                if let Ok(token) = self.renew_token().await {
                    req.headers_mut().append(
                        AUTHORIZATION,
                        HeaderValue::from_str(
                            format!("Bearer {}", token.get_access_token()).as_str(),
                        )
                        .unwrap(),
                    );
                }
            }
        }
        let duplicate_request = req.try_clone().ok_or_else(|| {
            Error::Middleware(anyhow!(
                "Request object is not cloneable. Are you passing a streaming body?".to_string()
            ))
        })?;
        match next.clone().run(duplicate_request, extensions).await {
            Ok(response) => {
                if response.status() == 401 {
                    error!("401 status code has been received, so trying to renew access token");
                    if let Ok(token) = self.renew_token().await {
                        req.headers_mut().remove(AUTHORIZATION);
                        req.headers_mut().append(
                            AUTHORIZATION,
                            HeaderValue::from_str(
                                format!("Bearer {}", token.get_access_token()).as_str(),
                            )
                            .unwrap(),
                        );
                        debug!("Renewed Api Token has been used successfully");
                        return next.run(req, extensions).await;
                    } else {
                        error!("Failed to get renewed access token");
                        return Err(Error::Middleware(anyhow!(
                            "Failed to get renewed access token"
                        )));
                    }
                    // refresh the token here
                } else {
                    return Ok(response);
                }
            }
            Err(error) => {
                return Err(error);
            }
        };
    }
}
