use super::token_middleware::API_TOKEN;
use crate::{client::token_middleware::TokenMiddleware, ApiError, ApiOptions};
use anyhow::Result;
use bytes::Bytes;
use futures_util::stream::Stream;
use http::header::AUTHORIZATION;
use logger::{debug, error, info, Level};
use reqwest::{
    header::{HeaderMap, HeaderValue, ACCEPT, CONTENT_TYPE},
    multipart::Form,
    Method, Response,
};
use reqwest_middleware::{ClientBuilder, ClientWithMiddleware};
use reqwest_retry::{policies::ExponentialBackoff, RetryTransientMiddleware};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use serde_json::json;
use std::{fmt::Debug, time::Duration};
use tokio::time::timeout;
use tokio_stream::StreamExt;

#[derive(Debug, Deserialize, Serialize)]
#[serde(untagged)]
enum ApiResponse<T> {
    WithResult { result: T },
    Direct(T),
}

pub struct ApiClient {
    options: ApiOptions,
    client: ClientWithMiddleware,
}

impl ApiClient {
    fn default_headers() -> HeaderMap {
        let mut headers = HeaderMap::new();

        headers.append(
            ACCEPT,
            HeaderValue::from_str("application/json, text/plain, */*").unwrap(),
        );
        headers.append(
            CONTENT_TYPE,
            HeaderValue::from_str("application/json").unwrap(),
        );

        headers
    }

    pub fn new(options: ApiOptions) -> Result<Self, ApiError> {
        debug!("Initialising api client with options {:?}", options);

        let retry_policy = ExponentialBackoff::builder()
            .retry_bounds(Duration::from_secs(30), Duration::from_secs(60 * 30))
            .jitter(reqwest_retry::Jitter::Bounded)
            .build_with_max_retries(options.retry());

        let client = ClientBuilder::new(
            reqwest::ClientBuilder::new()
                .danger_accept_invalid_certs(true)
                .default_headers(ApiClient::default_headers())
                .connect_timeout(Duration::from_secs(options.timeout()))
                .read_timeout(Duration::from_secs(180))
                .build()?,
        )
        .with(TokenMiddleware::new(options.clone()))
        // Trace HTTP requests. See the tracing crate to make use of these traces.
        // .with(TracingMiddleware::<TimeTrace>::new())
        // Retry failed requests.
        .with(
            RetryTransientMiddleware::new_with_policy(retry_policy)
                .with_retry_log_level(Level::ERROR.into()),
        )
        .build();

        Ok(ApiClient { options, client })
    }

    fn build_url<S: AsRef<str>>(&self, url: S) -> String {
        format!("{}/api{}", self.options.host().to_owned(), url.as_ref())
    }

    async fn send_request<T: DeserializeOwned + Debug, B: Serialize>(
        &self,
        path: String,
        method: Method,
        json_body: B,
        form_body: Option<Form>,
    ) -> Result<T, ApiError> {
        let url = self.build_url(path);

        let method_str = method.clone().to_string();

        let response: Result<Response, reqwest_middleware::Error> = if let Some(form_body) =
            form_body
        {
            let mut default_headers = HeaderMap::new();
            default_headers.append(
                ACCEPT,
                HeaderValue::from_str("application/json, text/plain, */*").unwrap(),
            );
            let token = API_TOKEN.get().unwrap().read().await;
            if let Some(token) = token.as_ref() {
                default_headers.append(
                    AUTHORIZATION,
                    HeaderValue::from_str(format!("Bearer {}", token.get_access_token()).as_str())
                        .unwrap(),
                );
            }
            let client = reqwest::ClientBuilder::new()
                .danger_accept_invalid_certs(true)
                .default_headers(ApiClient::default_headers())
                .connect_timeout(Duration::from_secs(30))
                .read_timeout(Duration::from_secs(180))
                .build()?;

            client
                .post(&url)
                .multipart(form_body)
                .headers(default_headers)
                .send()
                .await
                .map_err(|e| e.into())
        } else {
            let mut request_builder = self.client.request(method, &url);

            if method_str != "GET" {
                debug!(
                    "Sending [{}] {} with body {}",
                    method_str,
                    &url,
                    serde_json::to_string(&json_body)?
                );
                request_builder = request_builder.json(&json_body);
            } else {
                debug!("Sending [{}] {}", method_str, &url);
            }
            request_builder.send().await
        };

        self.process_response::<T>(format!("[{}] {}", method_str, url), response)
            .await
    }

    pub async fn get<T: DeserializeOwned + Debug, S: AsRef<str>>(
        &self,
        path: S,
    ) -> Result<T, ApiError> {
        // self.login().await?;

        self.send_request(path.as_ref().to_owned(), Method::GET, json!({}), None)
            .await
    }

    pub fn wrap_stream_to_timeout(
        stream: impl Stream<Item = Result<Bytes, reqwest::Error>>,
        timeout_secs: u64,
    ) -> impl Stream<Item = Result<Bytes, ApiError>> {
        stream.then({
            let timeout_duration = if timeout_secs > 0 {
                Duration::from_secs(timeout_secs)
            } else {
                Duration::MAX
            };
            move |item| {
                let timeout_duration = timeout_duration; // move into async block
                async move {
                    match timeout(timeout_duration, async { item }).await {
                        Ok(Ok(bytes)) => Ok(bytes),
                        Ok(Err(error)) => {
                            error!(?error, "Error Reading response chunk of file");
                            Err(ApiError::RequestError(error))
                        }
                        Err(_) => {
                            error!(
                                "Timeout of {} seconds occured before receiving next chunk",
                                timeout_duration.as_secs()
                            );
                            Err(ApiError::StreamChunkTimeoutError(
                                timeout_duration.as_secs(),
                            ))
                        }
                    }
                }
            }
        })
    }

    pub async fn stream<S: AsRef<str>>(
        &self,
        path: S,
        method: Option<&String>,
        body: Option<impl Serialize>,
        timeout_secs: u64,
    ) -> Result<impl Stream<Item = Result<Bytes, ApiError>>, ApiError> {
        let url = self.build_url(path);
        let request_method = method.map_or(Method::GET, |v| v.as_str().try_into().unwrap());
        let mut request_builder = self.client.request(request_method.clone(), &url);

        if let Some(b) = body {
            request_builder = request_builder.json(&b);
        }

        debug!("Sending [{}] {}", request_method, &url);

        let response = request_builder.send().await?;
        let status = response.status();

        if status.is_success() {
            debug!("{} Received status {}", url, status.as_str());
            info!(
                "{} Received content length {} bytes",
                url,
                response
                    .headers()
                    .get("content-length")
                    .map_or(HeaderValue::from_static("0"), |v| v.to_owned())
                    .to_str()
                    .unwrap()
            );

            let stream = response.bytes_stream();
            let timeout_stream = ApiClient::wrap_stream_to_timeout(stream, timeout_secs);

            Ok(timeout_stream)
        } else {
            let response_str = String::from_utf8_lossy(&response.bytes().await?).to_string();
            error!("Finished {} with status {}", url, status.as_str(),);
            Err(ApiError::StatusError(
                url,
                status.as_str().to_owned(),
                response_str,
            ))
        }
    }

    pub async fn post<T: DeserializeOwned + Debug, S: AsRef<str>>(
        &self,
        path: S,
        body: impl Serialize,
    ) -> Result<T, ApiError> {
        // self.login().await?;

        self.send_request(path.as_ref().to_owned(), Method::POST, body, None)
            .await
    }

    pub async fn post_multipart_form<T: DeserializeOwned + Debug, S: AsRef<str>>(
        &self,
        path: S,
        body: Form,
    ) -> Result<T, ApiError> {
        self.send_request(
            path.as_ref().to_owned(),
            Method::POST,
            json!({}),
            Some(body),
        )
        .await
    }

    pub async fn put<T: DeserializeOwned + Debug, S: AsRef<str>>(
        &self,
        path: S,
        body: impl Serialize,
    ) -> Result<T, ApiError> {
        // self.login().await?;

        self.send_request(path.as_ref().to_owned(), Method::PUT, body, None)
            .await
    }

    async fn process_response<T: DeserializeOwned + Debug>(
        &self,
        url: String,
        response: Result<Response, reqwest_middleware::Error>,
    ) -> Result<T, ApiError> {
        if response.as_ref().is_err() {
            error!(
                "Failed to complete request: {} error: {}",
                &url,
                response.as_ref().err().unwrap().to_string()
            );
            return Err(ApiError::RequestMiddleWareError(response.err().unwrap()));
        }

        let response = response.unwrap();

        let status = response.status();

        let full_response = response.bytes().await?;

        if status.is_success() {
            let response_result = serde_json::from_slice::<ApiResponse<T>>(&full_response);
            if let Err(error) = response_result {
                let response_str = String::from_utf8_lossy(&full_response).to_string();
                error!(
                    ?error,
                    "Failed to decode response of {} raw response is {}", url, response_str
                );
                Err(ApiError::JsonDecodeError(error.to_string(), response_str))
            } else {
                let decoded_response = match response_result.unwrap() {
                    ApiResponse::WithResult { result } => result,
                    ApiResponse::Direct(r) => r,
                };
                debug!("response of {} is", url);
                debug!("{:#?}", &decoded_response);
                Ok(decoded_response)
            }
        } else {
            let response_str = String::from_utf8_lossy(&full_response).to_string();
            error!(
                "Finished {} with status {} and response {}",
                url,
                status.as_str(),
                response_str
            );
            Err(ApiError::StatusError(
                url,
                status.as_str().to_owned(),
                response_str.to_string(),
            ))
        }
    }
}
