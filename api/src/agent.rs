use crate::{get_client, ApiError};
use anyhow::Result;
use database::models::{
    AgentConfig, ComplianceRule, Configuration, Deployment, DeploymentPolicy, EnrollmentStatus,
    Package, Patch, ServerConfig, Task,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;

pub async fn get_configuration(asset_id: i64) -> Result<AgentConfig, ApiError> {
    get_client()?
        .post::<AgentConfig, _>("/zirozen/config", json!({"asset_id": asset_id}))
        .await
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TaskDetail {
    pub deployment: Option<Deployment>,
    pub package: Option<Package>,
    pub patch: Option<Patch>,
    pub configuration: Option<Configuration>,
    pub compliance: Option<ComplianceRule>,
    pub policy: Option<DeploymentPolicy>,
    pub commands: Option<Value>,
    pub repo_block: Option<Value>,
    pub command_block: Option<Value>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RefreshCallResponse {
    #[serde(alias = "task")]
    pub tasks: Option<Vec<Task>>,
    pub task_details: Option<HashMap<String, TaskDetail>>,
}

pub async fn refresh_call(asset_id: i64) -> Result<RefreshCallResponse, ApiError> {
    let response = get_client()?
        .post::<RefreshCallResponse, _>(
            "/patch/agent/v2/refresh",
            json!({
                "assetId": asset_id
            }),
        )
        .await?;

    if response.tasks.is_none() {
        Ok(response)
    } else {
        let mut tasks = response.tasks.unwrap();
        if response.task_details.is_some() {
            let mut task_details = response.task_details.unwrap();
            for task in tasks.iter_mut() {
                if let Some(task_detail) = task_details.remove(&task.id.to_string()) {
                    task.deployment = task_detail.deployment;
                    task.package = task_detail.package;
                    task.patch = task_detail.patch;
                    task.configuration = task_detail.configuration;
                    task.compliance = task_detail.compliance;
                    task.policy = task_detail.policy;
                    task.commands = task_detail.commands;
                    if task_detail.command_block.is_some() && task_detail.repo_block.is_some() {
                        if task.commands.is_none() {
                            task.commands = Some(json!({}));
                        }
                        task.custom_task_details = Some(json!({
                            "repo_block": task_detail.repo_block.unwrap(),
                            "command_block": task_detail.command_block.unwrap()
                        }));
                    }
                }
            }
        }

        Ok(RefreshCallResponse {
            tasks: Some(tasks),
            task_details: None,
        })
    }
}

pub async fn request_enrollment(config: &ServerConfig) -> Result<EnrollmentStatus, ApiError> {
    Ok(get_client()?
        .post::<EnrollmentStatus, _>(
            "/zirozen/enroll",
            json!({
                "enroll_secret": config.enroll_secret(),
                "uuid": config.uuid(),
                "host_name": config.host(),
                "ip_addresses": config.ip_addresses()
            }),
        )
        .await?)
}

pub async fn provision_agent(agent_provision_data: Value) -> Result<Value, ApiError> {
    Ok(get_client()?
        .post::<Value, _>("/zirozen/provision", agent_provision_data)
        .await?)
}

pub async fn enroll_secret_from_id(enroll_id: i64) -> Result<String, ApiError> {
    let response = get_client()?
        .get::<Value, _>(format!("/inventory/enroll/{}", enroll_id))
        .await?;

    Ok(response
        .as_object()
        .unwrap()
        .get("secret")
        .unwrap()
        .as_str()
        .unwrap()
        .to_owned())
}

pub async fn is_endpointops_update_available(uuid: &str) -> Result<bool, ApiError> {
    let response = get_client()?
        .get::<Value, _>(format!("/zirozen/agent/upgrade/required/{}", uuid))
        .await?;

    Ok(response
        .as_object()
        .unwrap()
        .get("result")
        .map_or(Value::Bool(false), |i| i.to_owned())
        .as_bool()
        .unwrap()
        .to_owned())
}
