[package]
name = "api"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
shell = { path = "../shell" }
reqwest = { version = "0.12.22", features = [
  "json",
  "stream",
  "rustls-tls",
  "multipart",
], default-features = false }
serde_json = "1.0.140"
serde = { version = "1.0.219", features = ["derive"] }
reqwest-middleware = { version = "0.4.2", features = ["json", "multipart"] }
reqwest-retry = "0.7.0"
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
tokio = { version = "1.46.1", features = ["full", "tracing"] }
bytes = "1.10.1"
futures-util = "0.3.31"
tokio-stream = "0.1.17"
urlencoding = "2.1.3"
regex = "1.11.1"
http = "1.3.1"
async-trait = "0.1.88"
cfg-if = "1.0.1"
async-speed-limit = { version = "0.4.2", features = ["tokio"] }
tokio-util = "0.7.15"
