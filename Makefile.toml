[config]
default_to_workspace = false

[tasks.run-agent]
command = "cargo"
args = ["run", "--config", "target.\"cfg(all())\".runner=\"sudo -E\"", "--", "--agent", "--log=debug"]

[tasks.format]
install_crate = "rustfmt"
command = "cargo"
args = ["fmt", "--", "--emit=files"]

[tasks.clean-all]
private = true
command = "cargo"
args = ["clean"]

[tasks.clean-release]
private = true
command = "cargo"
args = ["clean", '--release']

## macos build steps
[tasks.macos-arm64]
script = '''
rm -rf target/aarch64-apple-darwin
cargo build --release --target aarch64-apple-darwin
if [ $? -ne 0 ]; then
  echo "macos-aarch64 build failed"
  exit 1
fi
echo "macos-aarch64 build is finished"
'''

[tasks.macos-x64]
script = '''
rm -rf target/x86_64-apple-darwin
cargo build --release --target x86_64-apple-darwin
if [ $? -ne 0 ]; then
  echo "macos-x64 build failed"
  exit 1
fi
echo "macos-x64 build is finished"
'''

[tasks.macos]
condition = { profiles = ["production"] }
dependencies = ["clean-release", "macos-arm64", "macos-x64"]
script = """
    rm -rf target/macos
    mkdir -p target/macos/mac_x64
    mkdir -p target/macos/mac_arm64
    cp target/aarch64-apple-darwin/release/endpointops target/macos/mac_arm64/endpointops
    cp target/aarch64-apple-darwin/release/build.txt target/macos/mac_arm64/build.txt
    cp target/x86_64-apple-darwin/release/endpointops target/macos/mac_x64/endpointops
    cp target/x86_64-apple-darwin/release/build.txt target/macos/mac_x64/build.txt
  """

### linux build steps
[tasks.linux-arm64]
script = '''
rm -rf target/aarch64-unknown-linux-gnu
cargo zigbuild --release --target aarch64-unknown-linux-gnu.2.17
if [ $? -ne 0 ]; then
  echo "linux-aarch64 build failed"
  exit 1
fi
echo "linux-aarch64 build is finished"
'''

[tasks.linux-x64]
script = '''
rm -rf target/x86_64-unknown-linux-gnu
cargo zigbuild --release --target x86_64-unknown-linux-gnu.2.17
if [ $? -ne 0 ]; then
  echo "linux-x64 build failed"
  exit 1
fi
echo "linux-x64 build is finished"
'''

[tasks.linux]
condition = { profiles = ["production"] }
dependencies = ["clean-release", "linux-arm64", "linux-x64"]
script = """
    rm -rf target/linux
    mkdir -p target/linux/linux_x64
    mkdir -p target/linux/linux_arm64
    cp target/aarch64-unknown-linux-gnu/release/endpointops target/linux/linux_arm64/endpointops
    cp target/aarch64-unknown-linux-gnu/release/build.txt target/linux/linux_arm64/build.txt
    cp target/x86_64-unknown-linux-gnu/release/endpointops target/linux/linux_x64/endpointops
    cp target/x86_64-unknown-linux-gnu/release/build.txt target/linux/linux_x64/build.txt
  """


### windows build steps
[tasks.windows-x64]
script_runner = "powershell"
script_extension = "ps1"
script = '''
Remove-Item -Path "target\x86_64-pc-windows-msvc" -Recurse -Force -ErrorAction SilentlyContinue
cargo build --release --target x86_64-pc-windows-msvc
if ($lastExitCode -ne 0) {
  echo "windows-x64 build failed"
  exit 1
}
echo "windows-x64 build is finished"
'''

[tasks.windows-arm64]
script_runner = "powershell"
script_extension = "ps1"
script = '''
Remove-Item -Path "target\aarch64-pc-windows-msvc" -Recurse -Force -ErrorAction SilentlyContinue
cargo build --release --target aarch64-pc-windows-msvc
if ($lastExitCode -ne 0) {
  echo "windows-arm64 build failed"
  exit 1
}
echo "windows-arm64 build is finished"
'''


[tasks.windows]
script_runner = "powershell"
script_extension = "ps1"
condition = { profiles = ["production"] }
dependencies = ["clean-release", "windows-x64", "windows-arm64"]
script = '''
    Remove-Item -Path "target\windows" -Recurse -Force -ErrorAction SilentlyContinue
    mkdir -p target\windows\windows_x64
    mkdir -p target\windows\windows_arm64
    cp target\x86_64-pc-windows-msvc\release\endpointops.exe target\windows\windows_x64\endpointops.exe -Force
    cp target\x86_64-pc-windows-msvc\release\build.txt target\windows\windows_x64\build.txt -Force
    cp target\aarch64-pc-windows-msvc\release\endpointops.exe target\windows\windows_arm64\endpointops.exe -Force
    cp target\aarch64-pc-windows-msvc\release\build.txt target\windows\windows_arm64\build.txt -Force
  '''
