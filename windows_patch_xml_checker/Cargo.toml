[package]
name = "windows_patch_xml_checker"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
shell = { path = "../shell" }
database = { path = "../database" }
windows_registry = { path = "../windows_registry" }
utils = { path = "../utils" }
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
serde = { version = "1.0.219", features = ["derive"] }
quick-xml = { version = "0.38.0", features = ["serialize"] }
evalexpr = "12.0.2"
wmi = "0.17"
version-compare = "0.2.0"
windows = { version = "0.61.3", features = [
  "Win32_UI_Shell",
  "Win32_Foundation",
  "Win32_Storage_FileSystem",
] }
chrono = "0.4.41"
serde_json = "1.0.140"
dashmap = { version = "6.1.0", features = ["rayon"] }
regex = "1.11.1"
