use crate::WindowsXmlCheckerError;
use logger::error;
use serde::de::DeserializeOwned;
use serde_json::Value;
use std::{collections::BTreeMap, fmt::Debug};
use wmi::{COMLibrary, Variant, WMIConnection};

pub struct WMIGetter {
    connection: WMIConnection,
}

pub fn variant_to_string(variant: &Variant) -> String {
    match variant {
        Variant::String(s) => s.clone(),
        Variant::Bool(b) => b.to_string(),
        Variant::Null => "null".to_string(),
        Variant::Array(arr) => format!("{:?}", arr),
        other => format!("{:?}", other), // fallback
    }
}

impl WMIGetter {
    pub fn new(namespace: Option<String>) -> Result<Self, WindowsXmlCheckerError> {
        let com = match COMLibrary::new() {
            Ok(com) => com,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to initialize com in wmi so assuming it is already initialised"
                );
                unsafe { COMLibrary::assume_initialized() }
            }
        };

        let connection = if let Some(namespace) = namespace {
            WMIConnection::with_namespace_path(&namespace, com.into())?
        } else {
            WMIConnection::new(com.into())?
        };

        Ok(Self { connection })
    }

    pub fn query(&self, query: &str) -> Vec<Value> {
        match self.connection.raw_query(query) {
            Ok(result) => result,
            Err(error) => {
                error!(?error, "Failed to execute query {}", query);
                vec![]
            }
        }
    }

    pub fn raw_query(&self, query: &str) -> Vec<BTreeMap<String, Variant>> {
        match self.connection.raw_query(query) {
            Ok(result) => result,
            Err(error) => {
                error!(?error, "Failed to execute query {}", query);
                vec![]
            }
        }
    }

    pub fn get<T: DeserializeOwned + Debug>(&self) -> Option<T> {
        match self.connection.query::<T>() {
            Ok(result) => {
                if result.len() > 0 {
                    Some(result.into_iter().nth(0).unwrap())
                } else {
                    error!("No data found in wmi query {:?}", result);
                    None
                }
            }
            Err(error) => {
                error!(?error, "Failed to query wmi connection");
                None
            }
        }
    }
}
