use crate::WmiOperatingSystemValues;
use logger::{debug, error};
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct WindowsLanguage {
    #[serde(rename = "@Language")]
    language: Option<String>,
}

impl WindowsLanguage {
    pub fn evaluate(&self, wmi_operating_system: &Option<WmiOperatingSystemValues>) -> bool {
        debug!("Inspecting Windows Language");
        if wmi_operating_system.is_some() {
            return wmi_operating_system
                .as_ref()
                .unwrap()
                .mui_languages
                .contains(self.language.as_ref().unwrap());
        }
        error!("No wmi_operating_system data found");
        return false;
    }
}
