use super::Operator;
use crate::{
    xml_parser::applicability_rules::expression_evaluator::ExpressionEvaluator,
    WindowsXmlCheckerError, WmiOperatingSystemValues,
};
use logger::{debug, error};
use serde::Deserialize;

#[derive(Debug, Default)]
pub struct SystemWinVersion {
    pub major: usize,
    pub minor: usize,
}

impl SystemWinVersion {
    pub fn new(version: String) -> Result<SystemWinVersion, WindowsXmlCheckerError> {
        let parts: Vec<&str> = version.split(".").collect();

        Ok(SystemWinVersion {
            major: parts[0].parse().map_err(|error| {
                WindowsXmlCheckerError::UnableToConvert(format!(
                    "Failed to convert to usize for windows version {} with error {:?}",
                    version, error
                ))
            })?,
            minor: parts[1].parse().map_err(|error| {
                WindowsXmlCheckerError::UnableToConvert(format!(
                    "Failed to convert to usize for windows version {} with error {:?}",
                    version, error
                ))
            })?,
        })
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct WindowsVersion {
    #[serde(rename = "@Comparison")]
    comparison: Option<Operator>,
    #[serde(rename = "@MinorVersion")]
    minor_version: Option<usize>,
    #[serde(rename = "@MajorVersion")]
    major_version: Option<usize>,
    #[serde(rename = "@ProductType")]
    product_type: Option<i16>,
    #[serde(rename = "@BuildNumber")]
    build_number: Option<String>,
    #[serde(rename = "@ServicePackMajor")]
    service_pack_major: Option<i16>,
    #[serde(rename = "@ServicePackMinor")]
    service_pack_minor: Option<i16>,
    #[serde(rename = "@SuiteMask")]
    suite_mask: Option<i16>,
}

impl WindowsVersion {
    fn match_system_values(&self, system_value: &WmiOperatingSystemValues) -> bool {
        let win_version = match SystemWinVersion::new(system_value.version.clone()) {
            Ok(version) => version,
            Err(error) => {
                error!(?error, "Failed to build windows system version");
                return false;
            }
        };
        let operator = self.comparison.as_ref().unwrap();
        if let Some(major_version) = self.major_version.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &win_version.major,
                major_version,
                operator,
                "MajorVersion",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(minor_version) = self.minor_version.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &win_version.minor,
                minor_version,
                operator,
                "MinorVersion",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(build_number) = self.build_number.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &system_value.build_number,
                build_number,
                operator,
                "BuildNumber",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(service_pack) = self.service_pack_major.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &system_value.service_pack_major_version,
                service_pack,
                operator,
                "ServicePackMajor",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(service_pack) = self.service_pack_minor.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &system_value.service_pack_minor_version,
                service_pack,
                operator,
                "ServicePackMinor",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(product_type) = self.product_type.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &system_value.product_type,
                product_type,
                operator,
                "ProductType",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(suite_mask) = self.suite_mask.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &system_value.suite_mask,
                suite_mask,
                operator,
                "SuiteMask",
            );
            if !evaluator.run() {
                return false;
            }
        }

        true
    }

    pub fn evaluate(&self, wmi_operating_system: &Option<WmiOperatingSystemValues>) -> bool {
        debug!("Inspecting Windows Version");
        if self.comparison.is_none() {
            error!("No operator found for the windows version rule {:?}", self);
            return false;
        }

        if wmi_operating_system.is_some() {
            return self.match_system_values(wmi_operating_system.as_ref().unwrap());
        }
        error!("No wmi_operating_system data found");
        return false;
    }
}
