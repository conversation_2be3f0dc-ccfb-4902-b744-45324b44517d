use crate::xml_parser::applicability_rules::version_evaluator::VersionEvaluator;

use super::Operator;
use logger::{debug, error};
use serde::Deserialize;
use std::path::PathBuf;
use windows_registry::WinRegistry;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ProductReleaseInstalled {
    #[serde(rename = "@Name")]
    pub name: Option<String>,
    #[serde(rename = "@Version")]
    pub version: Option<String>,
    #[serde(rename = "@Comparison")]
    pub comparison: Option<Operator>,
}

impl ProductReleaseInstalled {
    fn get_reg_value(&self) -> Option<String> {
        let path = PathBuf::from(
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Update\\TargetingInfo\\Installed",
        ).join(self.name.as_ref().unwrap());
        match WinRegistry::new(&path) {
            Ok(registry) => Some(registry.get_value::<String>("Version".to_owned())),
            Err(error) => {
                error!(
                    ?error,
                    "Failed to open registry key at path {}",
                    path.display()
                );
                None
            }
        }
    }

    pub fn evaluate(&self) -> bool {
        debug!("Checking ProductRelease Version");
        let operator = self
            .comparison
            .as_ref()
            .unwrap_or(&Operator::GreaterThanOrEqualTo);

        if self.name.is_none() || self.version.is_none() {
            error!("No name or version found in attributes {:?}", self);
            return false;
        }

        match self.get_reg_value() {
            Some(reg_value) => {
                if reg_value.is_empty() {
                    error!("Registry value is found empty");
                    false
                } else {
                    VersionEvaluator::new(
                        &reg_value,
                        self.version.as_ref().unwrap(),
                        operator,
                        "ProductReleaseInstalled",
                    )
                    .run()
                }
            }
            None => false,
        }
    }
}
