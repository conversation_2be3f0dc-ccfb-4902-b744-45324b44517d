use std::path::PathBuf;

use crate::xml_parser::applicability_rules::{
    expression_evaluator::ExpressionEvaluator, version_evaluator::VersionEvaluator,
};

use super::Operator;
use logger::{debug, error};
use serde::Deserialize;
use windows_registry::WinRegistry;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct Registry {
    #[serde(rename = "@Comparison")]
    comparison: Option<Operator>,
    #[serde(rename = "@Key")]
    key: String,
    #[serde(rename = "@Subkey")]
    sub_key: String,
    #[serde(rename = "@Value")]
    value: Option<String>,
    #[serde(rename = "@Data")]
    data: Option<String>,
    #[serde(rename = "@Type")]
    r#type: Option<String>,
    #[serde(rename = "@RegType32")]
    reg_type_32: Option<bool>,
}

impl Registry {
    fn get_system_value(&self) -> Option<String> {
        if self.value.is_none() {
            error!(
                "Value attribute is required to read system registry value {:?}",
                self
            );
            return None;
        }

        let path = PathBuf::from(format!("{}\\{}", self.key, self.sub_key));
        match WinRegistry::new(&path) {
            Ok(registry) => Some(if self.reg_type_32.is_some_and(|i| i) {
                registry
                    .get_value::<u32>(self.value.as_ref().unwrap().to_owned())
                    .to_string()
            } else {
                registry
                    .get_value::<String>(self.value.as_ref().unwrap().to_owned())
                    .to_string()
            }),
            Err(error) => {
                error!(?error, "Failed to open registry at path {}", path.display());
                None
            }
        }
    }

    pub fn value_exists(&self) -> bool {
        debug!("Inspecting Registry Value Exists");
        let path = PathBuf::from(format!("{}\\{}", self.key, self.sub_key));
        match WinRegistry::new(&path) {
            Ok(registry) => {
                if let Some(ref value) = self.value {
                    if let Some(ref _value_type) = self.r#type {
                        // @TODO check registry data type here
                        false
                    } else {
                        registry.has_value(value)
                    }
                } else {
                    true
                }
            }
            Err(error) => {
                error!(?error, "Failed to open registry at path {}", path.display());
                false
            }
        }
    }

    pub fn key_exists(&self) -> bool {
        debug!("Inspecting Registry Key Exists");
        let path = PathBuf::from(format!("{}\\{}", self.key, self.sub_key));
        match WinRegistry::new(&path) {
            Ok(registry) => {
                if let Some(ref value) = self.value {
                    registry.has_value(value)
                } else {
                    true
                }
            }
            Err(error) => {
                error!(?error, "Failed to open registry at path {}", path.display());
                false
            }
        }
    }

    pub fn evaluate_key_loop(&self) -> bool {
        debug!("Checking Registry Key Loop");

        debug!("Yet to implement this rule {:?}", self);

        false
    }

    pub fn evaluate_version(&self) -> bool {
        debug!("Inspecting Registry Version Comparision rule");
        if self.comparison.is_none() || self.data.is_none() {
            error!(
                "Comparision and Data is not provided in attribute {:?}",
                self
            );
            return false;
        }

        let operator = self.comparison.as_ref().unwrap();
        match self.get_system_value() {
            Some(value) => {
                if operator == &Operator::Contains {
                    debug!(
                        "Checking system value contains given data {}",
                        self.data.as_ref().unwrap()
                    );
                    value
                        .to_lowercase()
                        .contains(self.data.as_ref().unwrap().to_lowercase().as_str())
                } else {
                    VersionEvaluator::new(
                        &value,
                        self.data.as_ref().unwrap(),
                        operator,
                        "Registry Version Comparison",
                    )
                    .run()
                }
            }
            None => false,
        }
    }

    pub fn evaluate(&self) -> bool {
        debug!("Inspecting Registry Comparision rule");
        if self.comparison.is_none() || self.data.is_none() {
            error!(
                "Comparision and Data is not provided in attribute {:?}",
                self
            );
            return false;
        }

        let operator = self.comparison.as_ref().unwrap();
        match self.get_system_value() {
            Some(value) => {
                if operator == &Operator::Contains {
                    debug!(
                        "Checking system value contains given data {}",
                        self.data.as_ref().unwrap()
                    );
                    value
                        .to_lowercase()
                        .contains(self.data.as_ref().unwrap().to_lowercase().as_str())
                } else {
                    ExpressionEvaluator::new(
                        &value,
                        self.data.as_ref().unwrap(),
                        operator,
                        "Registry Comparison",
                    )
                    .run()
                }
            }
            None => false,
        }
    }
}
