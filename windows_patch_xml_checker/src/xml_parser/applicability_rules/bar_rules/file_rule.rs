use super::Operator;
use crate::xml_parser::applicability_rules::{
    expression_evaluator::ExpressionEvaluator, version_evaluator::VersionEvaluator,
};
use chrono::{DateTime, Utc};
use logger::{debug, error};
use serde::Deserialize;
use std::{
    ffi::{OsStr, OsString},
    fs,
    os::windows::{
        ffi::{OsStrExt, OsStringExt},
        fs::MetadataExt,
    },
    path::PathBuf,
};
use windows::{
    core::PCWSTR,
    Win32::Foundation::MAX_PATH,
    Win32::Storage::FileSystem::{
        GetFileVersionInfoSizeW, GetFileVersionInfoW, VerQueryValueW, VS_FIXEDFILEINFO,
    },
    Win32::UI::Shell::SHGetFolderPathW,
};
use windows_registry::WinRegistry;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct FileRule {
    #[serde(rename = "@Comparison")]
    comparison: Option<Operator>,
    #[serde(rename = "@Path")]
    path: Option<String>,
    #[serde(rename = "@Modified")]
    modified: Option<String>,
    #[serde(rename = "@Size")]
    size: Option<u64>,
    #[serde(rename = "@Version")]
    version: Option<String>,
    #[serde(rename = "@Csidl")]
    csidl: Option<i32>,
    #[serde(rename = "@Key")]
    key: Option<String>,
    #[serde(rename = "@Subkey")]
    sub_key: Option<String>,
    #[serde(rename = "@Value")]
    value: Option<String>,
}

impl FileRule {
    fn path_from_cisdl(&self) -> Option<PathBuf> {
        let csidl = self.csidl.unwrap();

        let file_path: Option<PathBuf>;

        unsafe {
            // Allocate buffer for the path
            let mut path: [u16; MAX_PATH as usize] = [0; MAX_PATH as usize];

            // Call SHGetFolderPathW
            let result = SHGetFolderPathW(None, csidl, None, 0, &mut path);

            // Check for errors
            file_path = if result.is_ok() {
                // Convert the path from UTF-16 to a Rust string
                let len = path.iter().position(|&c| c == 0).unwrap_or(path.len());
                let os_string = OsString::from_wide(&path[..len]);
                Some(os_string.into())
            } else {
                None
            };
        }
        if file_path.is_none() {
            error!("Failed to resolve path from csidl {}", csidl);
        }
        file_path
    }

    fn build_path(&self) -> Option<PathBuf> {
        if self.path.as_ref().is_none() || self.path.as_ref().is_some_and(|i| i.is_empty()) {
            error!("Path is empty when checking rule");
            return None;
        }
        if self.csidl.is_some() {
            if let Some(path) = self.path_from_cisdl() {
                Some(path.join(self.path.as_ref().unwrap()))
            } else {
                None
            }
        } else {
            Some(PathBuf::from(self.path.as_ref().unwrap()))
        }
    }

    fn build_path_from_registry(&self) -> Option<PathBuf> {
        let path = PathBuf::from(format!(
            "{}\\{}",
            self.key.as_ref().unwrap(),
            self.sub_key.as_ref().unwrap()
        ));
        match WinRegistry::new(&path) {
            Ok(registry) => {
                let value = registry.get_value::<String>(self.value.as_ref().unwrap().to_owned());
                if value.is_empty() {
                    error!("Got path value from registry empty");
                    None
                } else {
                    Some(PathBuf::from(value))
                }
            }
            Err(error) => {
                error!(?error, "Failed to open registry at path {}", path.display());
                None
            }
        }
    }

    pub fn file_exists(&self, from_registry: bool) -> bool {
        debug!("Evaluating File Exists rule");
        let path = match if from_registry {
            self.build_path_from_registry()
        } else {
            self.build_path()
        } {
            Some(path) => path,
            None => return false,
        };
        match path.try_exists() {
            Ok(result) => {
                if result {
                    debug!("File exists at path {}", path.display());
                } else {
                    error!("File doesn't exist at path {}", path.display());
                }
                result
            }
            Err(error) => {
                error!(?error, "Failed to check path exists {}", path.display());
                false
            }
        }
    }

    pub fn evaluate_modified(&self) -> bool {
        debug!("Inspecting File Modified Rule");
        if self.modified.is_none() {
            error!("No modified attribute found {:?}", self);
            return false;
        }
        if self.comparison.is_none() {
            error!("No comparision attribute found {:?}", self);
            return false;
        }
        let path = match self.build_path() {
            Some(path) => path,
            None => return false,
        };
        let expected_time: DateTime<Utc> = match self.modified.as_ref().unwrap().parse() {
            Ok(time) => time,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to parse modified attribute as date time {}",
                    self.modified.as_ref().unwrap()
                );
                return false;
            }
        };
        match fs::metadata(&path) {
            Ok(metadata) => match metadata.modified() {
                Ok(time) => {
                    let file_modification_time: DateTime<Utc> = time.into();
                    ExpressionEvaluator::new(
                        &file_modification_time.timestamp(),
                        &expected_time.timestamp(),
                        self.comparison.as_ref().unwrap(),
                        "File Modification Comparison",
                    )
                    .run()
                }
                Err(error) => {
                    error!(?error, "Failed to read modified time of file");
                    false
                }
            },
            Err(error) => {
                error!(
                    ?error,
                    "Failed to read file metadata at path {}",
                    path.display()
                );
                false
            }
        }
    }

    pub fn evaluate_file_size(&self) -> bool {
        debug!("Inspecting File Size rule");
        if self.size.is_none() {
            error!("No size attribute found {:?}", self);
            return false;
        }
        if self.comparison.is_none() {
            error!("No comparision attribute found {:?}", self);
            return false;
        }
        let path = match self.build_path() {
            Some(path) => path,
            None => return false,
        };
        match fs::metadata(&path) {
            Ok(metadata) => ExpressionEvaluator::new(
                &metadata.file_size(),
                self.size.as_ref().unwrap(),
                self.comparison.as_ref().unwrap(),
                "File Modification Comparison",
            )
            .run(),
            Err(error) => {
                error!(
                    ?error,
                    "Failed to read file metadata at path {}",
                    path.display()
                );
                false
            }
        }
    }

    pub fn evaluate_file_version(&self, from_registry: bool) -> bool {
        debug!("Inspecting File Version rule");
        if self.version.is_none() {
            error!("No Version attribute found {:?}", self);
            return false;
        }
        if self.comparison.is_none() {
            error!("No comparision attribute found {:?}", self);
            return false;
        }
        let path = match if from_registry {
            self.build_path_from_registry()
        } else {
            self.build_path()
        } {
            Some(path) => path,
            None => return false,
        };
        let current_version = match self.get_version(&path) {
            Some(version) => version,
            None => {
                error!("Failed to get version from file {}", path.display());
                return false;
            }
        };
        VersionEvaluator::new(
            &current_version,
            self.version.as_ref().unwrap(),
            self.comparison.as_ref().unwrap(),
            "File Version Comparison",
        )
        .run()
    }

    fn get_version(&self, path: &PathBuf) -> Option<String> {
        unsafe {
            // Path to the file
            let file_path = OsStr::new(path.to_str().unwrap())
                .encode_wide()
                .chain(Some(0).into_iter())
                .collect::<Vec<_>>();

            // Get the size of the version info
            let size = GetFileVersionInfoSizeW(PCWSTR(file_path.as_ptr()), None);
            if size == 0 {
                error!("Failed to get version info size");
                return None;
            }

            // Allocate buffer for version info
            let mut buffer = vec![0u8; size as usize];

            // Get the version info
            if GetFileVersionInfoW(
                PCWSTR(file_path.as_ptr()),
                None,
                size,
                buffer.as_mut_ptr() as *mut _,
            )
            .is_err()
            {
                error!("Failed to get version info");
                return None;
            }

            // Query the version value
            let mut ffi: *mut VS_FIXEDFILEINFO = std::ptr::null_mut();
            let mut len: u32 = 0;
            if VerQueryValueW(
                buffer.as_ptr() as *const _,
                PCWSTR("\\\0".encode_utf16().collect::<Vec<_>>().as_ptr()),
                &mut ffi as *mut _ as *mut _,
                &mut len,
            )
            .as_bool()
                == false
            {
                error!("Failed to query version value");
                return None;
            }

            // Print version info
            if ffi.is_null() {
                error!("FFI call received null");
                return None;
            }
            let ffi = &*ffi;
            let file_version = (
                (ffi.dwFileVersionMS >> 16) & 0xffff,
                (ffi.dwFileVersionMS >> 0) & 0xffff,
                (ffi.dwFileVersionLS >> 16) & 0xffff,
                (ffi.dwFileVersionLS >> 0) & 0xffff,
            );
            debug!(
                "Got File Version {}.{}.{}.{}",
                file_version.0, file_version.1, file_version.2, file_version.3
            );
            Some(format!(
                "{}.{}.{}.{}",
                file_version.0, file_version.1, file_version.2, file_version.3
            ))
        }
    }
}
