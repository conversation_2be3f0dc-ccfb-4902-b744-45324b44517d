use crate::{ApplicabilityRulesEvaluationConfig, ParsedPackage, Rules};
use logger::debug;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct LarOr {
    #[serde(rename = "$value")]
    rules: Vec<Rules>,
}

impl LarOr {
    pub fn evaluate(
        &self,
        package: &ParsedPackage,
        config: &ApplicabilityRulesEvaluationConfig,
    ) -> bool {
        let mut result = false;
        for rule in &self.rules {
            if rule.evaluate(package, config) {
                result = true;
                break;
            }
        }
        debug!("lar:Or rules evaluation result {}", result);
        result
    }
}
