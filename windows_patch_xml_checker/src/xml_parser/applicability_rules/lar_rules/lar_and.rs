use crate::{ApplicabilityRules, ApplicabilityRulesEvaluationConfig, ParsedPackage, Rules};
use logger::debug;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct LarAnd {
    #[serde(rename = "$value")]
    rules: Vec<Rules>,
}

impl LarAnd {
    pub fn evaluate(
        &self,
        package: &ParsedPackage,
        config: &ApplicabilityRulesEvaluationConfig,
    ) -> bool {
        let result = ApplicabilityRules::evaluate_rules(&self.rules, package, config);

        debug!("lar:And rules evaluation result {}", result);

        result
    }
}
