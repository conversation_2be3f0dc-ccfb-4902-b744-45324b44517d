use super::bar_rules::Operator;
use logger::{debug, error};
use version_compare::Version;

pub struct VersionEvaluator<'a> {
    current_value: &'a str,
    expected_value: &'a str,
    operator: &'a Operator,
    rule_key_name: &'a str,
}

impl<'a> VersionEvaluator<'a> {
    pub fn new(
        current_value: &'a str,
        expected_value: &'a str,
        operator: &'a Operator,
        rule_key_name: &'a str,
    ) -> Self {
        Self {
            current_value,
            expected_value,
            operator,
            rule_key_name,
        }
    }

    pub fn run(&self) -> bool {
        let current_version = match Version::from(self.current_value) {
            Some(v) => v,
            None => {
                error!("Failed to parse string {} as version", self.current_value);
                return false;
            }
        };
        let expected_version = match Version::from(self.expected_value) {
            Some(v) => v,
            None => {
                error!("Failed to parse string {} as version", self.expected_value);
                return false;
            }
        };

        let result = match self.operator {
            Operator::GreaterThanOrEqualTo => current_version >= expected_version,
            Operator::GreaterThan => current_version > expected_version,
            Operator::EqualTo => current_version == expected_version,
            Operator::LessThan => current_version < expected_version,
            Operator::LessThanOrEqualTo => current_version <= expected_version,
            Operator::Contains => self.current_value.contains(self.expected_value),
        };

        debug!(
            "{}: Comparision of version {} {} {} is evaluated to {}",
            self.rule_key_name,
            current_version.to_string(),
            self.operator.to_string(),
            expected_version.to_string(),
            result
        );
        result
    }
}
