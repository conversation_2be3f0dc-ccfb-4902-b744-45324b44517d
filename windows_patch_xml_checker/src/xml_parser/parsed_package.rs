use super::ZirozenProperties;
use super::{
    ApplicabilityRules, ApplicabilityRulesEvaluationConfig, ApplicabilityRulesEvaluationResult,
    PatchStatus, RelationEvaluationConfig, Relationships, UpdateIdentity, UpdateProperties,
    UpdateType,
};
use logger::debug;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ParsedPackage {
    update_identity: UpdateIdentity,
    properties: UpdateProperties,
    zirozen_properties: Option<ZirozenProperties>,
    relationships: Option<Relationships>,
    applicability_rules: Option<ApplicabilityRules>,
}

impl ParsedPackage {
    pub fn has_relationship(&self) -> bool {
        self.relationships.is_some()
    }

    pub fn is_third_party_patch(&self) -> bool {
        self.zirozen_properties.as_ref().is_some_and(|z| {
            z.is_third_party_patch
                .as_ref()
                .is_some_and(|v| v.as_str().to_lowercase() == "yes")
        })
    }

    pub fn has_prerequisites(&self) -> bool {
        self.relationships
            .as_ref()
            .is_some_and(|r| r.has_prerequisites())
    }

    pub fn has_bundled_updates(&self) -> bool {
        self.relationships
            .as_ref()
            .is_some_and(|r| r.has_bundled_updates())
    }

    pub fn has_applicability_rules(&self) -> bool {
        self.applicability_rules.is_some()
    }

    pub fn evaluate_applicability_rules(
        &self,
        config: &ApplicabilityRulesEvaluationConfig,
    ) -> ApplicabilityRulesEvaluationResult {
        if let Some(rules) = &self.applicability_rules {
            rules.evaluate_applicability_rules(self, config)
        } else {
            ApplicabilityRulesEvaluationResult::default()
        }
    }

    pub fn evaluate_prerequsite_relationship(
        &self,
        config: &RelationEvaluationConfig,
    ) -> PatchStatus {
        debug!(
            "Evaluating prerequesite relationship at level {}",
            config.level
        );
        if let Some(relationship) = self.relationships.as_ref() {
            relationship.evaluate_prerequisites(config)
        } else {
            PatchStatus::Installed
        }
    }

    pub fn evaluate_bundled_relationship(&self, config: &RelationEvaluationConfig) -> PatchStatus {
        debug!(
            "Evaluating bundled update relationship at level {}",
            config.level
        );
        if let Some(relationship) = self.relationships.as_ref() {
            relationship.evaluate_bundled_updates(config)
        } else {
            PatchStatus::Installed
        }
    }

    pub fn get_kb(&self) -> String {
        self.properties
            .kb_id
            .as_ref()
            .map_or("NONE".to_owned(), |v| v.to_owned())
    }

    pub fn get_uuid(&self) -> &str {
        self.update_identity.get_uuid()
    }

    pub fn get_update_type(&self) -> UpdateType {
        self.properties
            .update_type
            .as_ref()
            .map_or(UpdateType::Unknown, |f| f.to_owned())
    }

    pub fn get_update_properites(&self) -> &UpdateProperties {
        &self.properties
    }
}
