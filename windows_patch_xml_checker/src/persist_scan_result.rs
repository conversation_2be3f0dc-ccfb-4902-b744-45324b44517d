use crate::{<PERSON><PERSON><PERSON><PERSON>, WindowsUpdate};
use database::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Key};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize, Default)]
pub struct PersistScanResult {
    pub uuid: PrimaryKey,
    pub status: PatchStatus,
    pub timestamp: u64,
    pub update_type: String,
}

impl HasPrimaryKey for PersistScanResult {
    fn table_name(&self) -> &str {
        "patch_file_result"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.uuid
    }
}

impl Model for PersistScanResult {}

impl Into<PersistScanResult> for &WindowsUpdate {
    fn into(self) -> PersistScanResult {
        PersistScanResult {
            uuid: self.get_parsed_package().get_uuid().to_lowercase().into(),
            status: self.get_status(),
            timestamp: self.timestamp,
            update_type: self.get_parsed_package().get_update_type().into(),
        }
    }
}
