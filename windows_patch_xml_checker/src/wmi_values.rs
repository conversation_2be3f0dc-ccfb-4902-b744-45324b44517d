use crate::WMIGetter;
use logger::error;
use serde::Deserialize;

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_OperatingSystem")]
#[serde(rename_all = "PascalCase")]
pub struct WmiOperatingSystemValues {
    pub mui_languages: Vec<String>,
    pub portable_operating_system: bool,
    pub operating_system_sku: usize,
    pub version: String,
    pub build_number: String,
    pub product_type: i16,
    pub service_pack_major_version: i16,
    pub service_pack_minor_version: i16,
    pub suite_mask: i16,
}

impl WmiOperatingSystemValues {
    pub fn build() -> Option<Self> {
        match WMIGetter::new(None) {
            Ok(wmi_connection) => wmi_connection.get::<Self>(),
            Err(error) => {
                error!(?error, "Failed to open wmi connection to read system value");
                None
            }
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_Processor")]
#[serde(rename_all = "PascalCase")]
pub struct WmiProcessorValues {
    pub architecture: i16,
    pub level: i16,
    pub revision: Option<i16>,
    pub number_of_cores: i16,
}

impl WmiProcessorValues {
    pub fn build() -> Option<Self> {
        match WMIGetter::new(None) {
            Ok(wmi_connection) => wmi_connection.get::<Self>(),
            Err(error) => {
                error!(?error, "Failed to open wmi connection to read system value");
                None
            }
        }
    }
}
