use anyhow::Error as AnyhowError;
use std::io;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AuthEventError {
    #[error("Auth Event Error: Unable to create/read file")]
    IOError(#[from] io::Error),

    #[cfg(windows)]
    #[error("Auth Event Error: Unable to subscribe windows event log")]
    FailedToSubscribeToEventLog(windows::core::Error),

    #[error("Auth Event Error: Unknown error")]
    UnknownError(#[from] AnyhowError),
}
