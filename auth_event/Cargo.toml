[package]
name = "auth_event"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
database = { path = "../database" }
thiserror = "2.0.12"
anyhow = { version = "1.0.98", features = ["backtrace"] }
cfg-if = "1.0.1"
serde = "1.0.219"
async-trait = "0.1.88"
chrono = { version = "0.4.41", features = ["alloc"] }
tokio = { version = "1.46.1", features = ["full", "tracing"] }


[target.'cfg(target_os = "linux")'.dependencies]
inotify = "0.11"
tokio-stream = "0.1.17"
regex = "1"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.61", features = [
  "Win32_Foundation",
  "Win32_System_EventLog",
  "Win32_System_WindowsProgramming",
] }
